package external

import (
	"time"
)

// ThirdRiskQueryRequest 第三方风控查询请求数据
type ThirdRiskQueryRequest struct {
	Service   string `json:"service"`   // 服务码
	UserName  string `json:"userName"`  // 用户姓名
	IDCard    string `json:"idCard"`    // 身份证号
	Telephone string `json:"telephone"` // 手机号
}

// ThirdAPICommonRequest 第三方API通用请求结构
type ThirdAPICommonRequest struct {
	AppID   string `json:"appId"`   // 客户ID
	BizData string `json:"bizData"` // 加密后的业务数据
}

// ThirdRiskResponse 第三方风控查询响应
type ThirdRiskResponse struct {
	Code     string                 `json:"code"`     // 状态码，标识请求结果
	OrderId  string                 `json:"orderId"`  // 查询单号，唯一标识
	ErrorMsg string                 `json:"errorMsg"` // 结果描述信息
	Data     *ThirdRiskResponseData `json:"data"`     // 业务结果具体数据
}

// ThirdRiskResponseData 第三方风控查询业务数据
type ThirdRiskResponseData struct {
	AuditResult string        `json:"audit_result"`          // 审核建议："OK"（通过）、"MANUAL"（人工审核）、"DENY"（拒绝）
	DenyReason  string        `json:"deny_reason,omitempty"` // 拒绝原因，仅当audit_result为"DENY"时可能返回
	DwfScore    string        `json:"dwfScore,omitempty"`    // 风控3 分数 当 audit_result 为 "MANUAL" 时返回
	LeidaV4     *LeidaV4Data  `json:"leida_v4,omitempty"`    // 雷达v4数据
	TanZhenC    *TanZhenCData `json:"tan_zhen_c,omitempty"`  // 探针C数据
	Zwsc        *ZwscData     `json:"zwsc,omitempty"`        // Zwsc数据
}

// LeidaV4Data 雷达v4数据结构
type LeidaV4Data struct {
	ApplyReportDetail    map[string]string `json:"apply_report_detail,omitempty"`    // 申请报告详情，以A开头的字段
	BehaviorReportDetail map[string]string `json:"behavior_report_detail,omitempty"` // 行为报告详情，以B开头的字段
	CurrentReportDetail  map[string]string `json:"current_report_detail,omitempty"`  // 当前报告详情，以C开头的字段
}

// TanZhenCData 探针C数据结构
type TanZhenCData struct {
	AccExc                string `json:"acc_exc,omitempty"`                 // 账户异常情况
	AccSleep              string `json:"acc_sleep,omitempty"`               // 睡眠账户数量
	CountPerformance      string `json:"count_performance,omitempty"`       // 履约次数
	CurrentlyOverdue      string `json:"currently_overdue,omitempty"`       // 当前逾期情况
	CurrentlyPerformance  string `json:"currently_performance,omitempty"`   // 当前履约情况
	LatestPerformanceTime string `json:"latest_performance_time,omitempty"` // 最新履约时间
	MaxPerformanceAmt     string `json:"max_performance_amt,omitempty"`     // 最大履约金额
	ResultCode            string `json:"result_code,omitempty"`             // 结果代码
}

// ZwscData Zwsc数据结构
type ZwscData struct {
	Min         *int   `json:"min,omitempty"`
	Max         *int   `json:"max,omitempty"`
	QueryResult string `json:"queryResult,omitempty"`
}

// GoDemoResult 第三方风控服务返回的完整结果
type GoDemoResult struct {
	AuditResult  string    `json:"audit_result"`          // 审核建议："OK"（通过）、"MANUAL"（人工审核）、"DENY"（拒绝）
	DenyReason   string    `json:"deny_reason,omitempty"` // 拒绝原因
	DwfScore     string    `json:"dwf_score,omitempty"`   // 风控3 分数 当 audit_result 为 "MANUAL" 时参考
	LeidaV4Data  string    `json:"leida_v4_data"`         // 雷达v4数据（JSON字符串）
	TanZhenCData string    `json:"tan_zhen_c_data"`       // 探针C数据（JSON字符串）
	ZwscData     string    `json:"zwsc_data"`             // Zwsc数据（JSON字符串）
	CreatedAt    time.Time `json:"created_at"`            // 创建时间
}

// IsSuccess 判断响应是否成功
func (r *ThirdRiskResponse) IsSuccess() bool {
	return r.Code == "JMR0000"
}

// GetErrorMessage 获取错误消息
func (r *ThirdRiskResponse) GetErrorMessage() string {
	return r.ErrorMsg
}

// GetAuditResult 获取审核结果
func (r *ThirdRiskResponse) GetAuditResult() string {
	if r.Data != nil {
		return r.Data.AuditResult
	}
	return ""
}

// GetDenyReason 获取拒绝原因
func (r *ThirdRiskResponse) GetDenyReason() string {
	if r.Data != nil {
		return r.Data.DenyReason
	}
	return ""
}
