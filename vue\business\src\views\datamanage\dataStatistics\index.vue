<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import {
    IconRefresh,
    IconSearch,
    IconUserAdd,
  } from '@arco-design/web-vue/es/icon';
  import dayjs from 'dayjs';
  const queryFormRef = ref();
  const queryForm = reactive({});
  const fetchData = async () => {};

  const columns = [
    {
      title: 'No',
      dataIndex: 'no',
    },
    {
      title: '日期',
      dataIndex: 'a1',
      width: 100,
    },
    {
      title: '注册数',
      dataIndex: 'a2',
      width: 100,
    },
    {
      title: '实名数',
      dataIndex: 'a3',
      width: 130,
    },
    {
      title: '机审',
      children: [
        {
          title: '进件数',
          dataIndex: 'a41',
          width: 100,
        },
        {
          title: '新用户进件数',
          dataIndex: 'a42',
          width: 150,
        },
        {
          title: '通过数',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '通过率',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '新用户通过数',
          dataIndex: 'a42',
          width: 150,
        },
        {
          title: '新用户通过率',
          dataIndex: 'a42',
          width: 150,
        },
      ],
    },
    {
      title: '下单数',
      dataIndex: 'a5',
      width: 100,
    },
    {
      title: '成交数',
      children: [
        {
          title: '总',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '实际',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '初购',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '复购',
          dataIndex: 'a42',
          width: 100,
        },
        {
          title: '加贷数量',
          dataIndex: 'a42',
          width: 100,
        },
      ]
    },
    {
      title: '合同金额',
      dataIndex: 'a7',
      width: 100,
    },
    {
      title: '成本',
      dataIndex: 'a8',
      width: 100,
    },
    {
      title: '注册成交率',
      dataIndex: 'a9',
      width: 120,
    },
    {
      title: '新用户通过成交率',
      dataIndex: 'a10',
      width: 150,
    }
  ];
  const dataSource = ref([{}]);
  // 分页
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    showTotal: true,
    showPageSize: true,
  });
  const selectedKeys = ref([]);

  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  // 分页变化
  const handlePageChange = (page: number) => {
    pagination.current = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.current = 1;
    fetchData();
  };

  const handleSearch = () => {
    pagination.current = 1;
    fetchData();
  };
  const handleReset = async () => {
    queryFormRef.value.resetFields();
    pagination.current = 1;
    fetchData();
  };
</script>

<template>
  <div class="container">
    <!-- 数据统计 -->
    <a-card title="数据统计" :bordered="false">
      <div class="stat-grid">
        <a-row :gutter="[24, 12]">
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">注册数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">实名数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户机审进件数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">机审进件数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户机审通过数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">机审通过数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">下单数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">实际成交数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">总成交数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">初购成交数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">复购成交数</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">加货数量</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">合同金额</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">成本</div>
              <div class="stat-number">1000</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">新用户机审通过率</div>
              <div class="stat-number">100%</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">机审通过率</div>
              <div class="stat-number">100%</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">注册转化率</div>
              <div class="stat-number">100%</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">成交转化率</div>
              <div class="stat-number">100%</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 数据统计表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item name="name" label="日期">
                <a-range-picker
                  :shortcuts="[
                    {
                      label: '近一周',
                      value: () => [dayjs().subtract(1, 'week'), dayjs()],
                    },
                    {
                      label: '近一月',
                      value: () => [dayjs().subtract(1, 'month'), dayjs()],
                    }
                  ]"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 数据统计列表 -->
    <a-card class="table-card" title="数据统计列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: '100%' }"
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
  .container {
    padding: 10px;
  }

  .stat-grid {
    .arco-col {
      height: 80px;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;
      background-color: var(--color-bg-1);
      padding: 16px;
      border-radius: 5px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .stat-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-2);
      }

      .stat-number {
        font-size: 16px;
        color: rgb(var(--primary-6));
        margin-top: 10px;
        font-weight: bold;
      }
    }
  }

  .due-form {
    margin-top: 20px;
  }

  .table-card {
    margin-top: 10px;
  }
</style>