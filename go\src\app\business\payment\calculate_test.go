package payment

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestCalculateRepaymentSchedule 测试计算还款计划接口
func TestCalculateRepaymentSchedule(t *testing.T) {
	// 设置 Gin 为测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试用例
	tests := []struct {
		name           string
		request        CalculateScheduleRequest
		expectedStatus int
		expectError    bool
	}{
		{
			name: "正常计算-后置付款",
			request: CalculateScheduleRequest{
				LoanAmount:         10000.00,
				LoanPeriod:         30,
				TotalPeriods:       3,
				GuaranteeFee:       500.00,
				AnnualInterestRate: 15.0,
				OtherFees:          100.00,
				PreInterest:        0,
				PreGuaranteeFee:    0,
				PrePrincipal:       0,
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "正常计算-前置付款",
			request: CalculateScheduleRequest{
				LoanAmount:         10000.00,
				LoanPeriod:         30,
				TotalPeriods:       3,
				GuaranteeFee:       500.00,
				AnnualInterestRate: 15.0,
				OtherFees:          100.00,
				PreInterest:        100.00,
				PreGuaranteeFee:    100.00,
				PrePrincipal:       100.00,
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "参数验证失败-金额为0",
			request: CalculateScheduleRequest{
				LoanAmount:         0,
				LoanPeriod:         30,
				TotalPeriods:       3,
				GuaranteeFee:       500.00,
				AnnualInterestRate: 15.0,
				OtherFees:          100.00,
				PreInterest:        0,
				PreGuaranteeFee:    0,
				PrePrincipal:       0,
			},
			expectedStatus: http.StatusOK, // 业务错误返回200但包含错误信息
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建请求体
			requestBody, err := json.Marshal(tt.request)
			assert.NoError(t, err)

			// 创建 HTTP 请求
			req, err := http.NewRequest("POST", "/api/payments/calculate-schedule", bytes.NewBuffer(requestBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			// 创建响应记录器
			w := httptest.NewRecorder()

			// 创建 Gin 路由
			router := gin.New()
			manager := &Manager{}
			router.POST("/api/payments/calculate-schedule", manager.CalculateRepaymentSchedule)

			// 执行请求
			router.ServeHTTP(w, req)

			// 验证状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectError {
				// 验证错误响应 (code != 0 表示失败)
				code, ok := response["code"].(float64)
				assert.True(t, ok)
				assert.NotEqual(t, 0.0, code)
				assert.NotEmpty(t, response["message"])
			} else {
				// 验证成功响应 (code == 0 表示成功)
				code, ok := response["code"].(float64)
				assert.True(t, ok)
				assert.Equal(t, 0.0, code)
				assert.NotNil(t, response["data"])

				// 验证返回的数据结构
				data, ok := response["data"].(map[string]interface{})
				assert.True(t, ok)
				assert.Contains(t, data, "total_periods")
				assert.Contains(t, data, "periods")
				assert.Contains(t, data, "total_principal")
				assert.Contains(t, data, "total_interest")
				assert.Contains(t, data, "total_repayable_amount")
				assert.Contains(t, data, "disbursement_amount")
			}
		})
	}
}

// TestCalculateScheduleRequestValidation 测试请求参数验证
func TestCalculateScheduleRequestValidation(t *testing.T) {
	// 测试 JSON Schema 验证
	schema := GetCalculateScheduleRequestSchema()
	assert.Equal(t, "计算还款计划请求参数", schema.Title)
	assert.Equal(t, "object", schema.Type)

	// 验证必填字段
	expectedRequired := []string{"loan_amount", "loan_period", "total_periods", "guarantee_fee", "annual_interest_rate", "other_fees", "is_pre_payment"}
	assert.Equal(t, expectedRequired, schema.Required)

	// 验证字段属性
	assert.Contains(t, schema.Properties, "loan_amount")
	assert.Contains(t, schema.Properties, "loan_period")
	assert.Contains(t, schema.Properties, "total_periods")
	assert.Contains(t, schema.Properties, "annual_interest_rate")
	assert.Contains(t, schema.Properties, "guarantee_fee")
	assert.Contains(t, schema.Properties, "other_fees")
	assert.Contains(t, schema.Properties, "is_pre_payment")
}
