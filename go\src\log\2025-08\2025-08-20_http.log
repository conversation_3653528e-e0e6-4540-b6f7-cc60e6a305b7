{"level":"dev.info","ts":"[2025-08-20 10:46:22.588]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"185d59a21f3b5c28fa774cd6","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-08-20 10:46:22.600]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"185d59a21f3b5c28fa774cd6","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0127074,"response_size":959}
