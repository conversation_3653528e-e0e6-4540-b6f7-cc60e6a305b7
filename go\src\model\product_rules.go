package model

import (
	"fmt"
	"time"
)

// ProductRules 产品规则数据模型（对应 product_rules 表）
type ProductRules struct {
	ID                 int     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`     // 主键ID，自增
	RuleName           string  `json:"rule_name" db:"rule_name"`                       // 规则名称，用于标识不同的产品规则
	LoanAmount         float64 `json:"loan_amount" db:"loan_amount"`                   // 贷款金额，单位：元
	LoanPeriod         int     `json:"loan_period" db:"loan_period"`                   // 贷款期限，单位：天
	TotalPeriods       int     `json:"total_periods" db:"total_periods"`               // 总还款期数，分期还款的期数
	GuaranteeFee       float64 `json:"guarantee_fee" db:"guarantee_fee"`               // 担保费，单位：元
	AnnualInterestRate float64 `json:"annual_interest_rate" db:"annual_interest_rate"` // 年利率，百分比形式（如：15.5表示15.5%）
	OtherFees          float64 `json:"other_fees" db:"other_fees"`                     // 其他费用，单位：元
	PrePrincipal       float64 `json:"pre_principal" db:"pre_principal"`               // 前置本金，单位：元
	PreInterest        float64 `json:"pre_interest" db:"pre_interest"`                 // 前置利息，单位：元
	PreGuaranteeFee    float64 `json:"pre_guarantee_fee" db:"pre_guarantee_fee"`       // 前置担保费，单位：元
	RuleCategory       string  `json:"rule_category" db:"rule_category"`               // 规则类别：新用户/复购用户
	RepaymentMethod    string  `json:"repayment_method" db:"repayment_method"`         // 还款方式：后置付款/前置付款（前置付款会在放款时扣除利息和费用）
	CreatedAt          int64   `json:"created_at" db:"created_at"`                     // 创建时间，Unix时间戳
	UpdatedAt          int64   `json:"updated_at" db:"updated_at"`                     // 更新时间，Unix时间戳
	DeletedAt          int64   `json:"deleted_at" db:"deleted_at"`                     // 删除时间，Unix时间戳
}

// TableName 指定表名
func (ProductRules) TableName() string {
	return "product_rules"
}

// IsPrePayment 判断是否为前置还款
func (p *ProductRules) IsPrePayment() bool {
	return p.RepaymentMethod == "前置付款"
}

// IsPostPayment 判断是否为后置还款
func (p *ProductRules) IsPostPayment() bool {
	return p.RepaymentMethod == "后置付款"
}

// ProductRulesService 产品规则服务
type ProductRulesService struct{}

// GetProductRulesService 创建产品规则服务实例（简化版本）
func GetProductRulesService() *ProductRulesService {
	return &ProductRulesService{}
}

// NewProductRulesService 创建产品规则服务实例（保持向后兼容）
func NewProductRulesService() *ProductRulesService {
	return GetProductRulesService()
}

// GetProductRuleByID 根据ID获取产品规则
func (s *ProductRulesService) GetProductRuleByID(id int) (*ProductRules, error) {
	data, err := DB().Table("product_rules").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("产品规则不存在")
	}

	var rule ProductRules
	if err := mapToStruct(data, &rule); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &rule, nil
}

// GetProductRulesByIDs 根据ID列表获取产品规则
func (s *ProductRulesService) GetProductRulesByIDs(ids []uint64) ([]*ProductRules, error) {
	if len(ids) == 0 {
		return []*ProductRules{}, nil
	}
	// 转换字符串ID为整数
	intIDs := make([]interface{}, len(ids))
	for i, id := range ids {
		intIDs[i] = id
	}

	data, err := DB().Table("product_rules").WhereIn("id", intIDs).Get()
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	var rules []*ProductRules
	for _, item := range data {
		rule := &ProductRules{} // 先初始化
		if err := mapToStruct(item, rule); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		rules = append(rules, rule)
	}

	return rules, nil
}

// GetActiveProductRules 获取所有有效的产品规则
func (s *ProductRulesService) GetActiveProductRules() ([]ProductRules, error) {
	data, err := DB().Table("product_rules").OrderBy("id ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	var rules []ProductRules
	for _, item := range data {
		var rule ProductRules
		if err := mapToStruct(item, &rule); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		rules = append(rules, rule)
	}

	return rules, nil
}

// GetProductRulesByCategory 根据规则类别获取产品规则
func (s *ProductRulesService) GetProductRulesByCategory(category string) ([]ProductRules, error) {
	data, err := DB().Table("product_rules").Where("rule_category", category).OrderBy("id ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	var rules []ProductRules
	for _, item := range data {
		var rule ProductRules
		if err := mapToStruct(item, &rule); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		rules = append(rules, rule)
	}

	return rules, nil
}

// CreateProductRule 创建产品规则
func (s *ProductRulesService) CreateProductRule(rule *ProductRules) error {
	if rule == nil {
		return fmt.Errorf("产品规则对象不能为空")
	}

	// 设置创建时间
	now := time.Now().Unix()
	rule.CreatedAt = now
	rule.UpdatedAt = now

	// 构建插入数据
	data := map[string]interface{}{
		"rule_name":            rule.RuleName,
		"loan_amount":          rule.LoanAmount,
		"loan_period":          rule.LoanPeriod,
		"total_periods":        rule.TotalPeriods,
		"guarantee_fee":        rule.GuaranteeFee,
		"annual_interest_rate": rule.AnnualInterestRate,
		"other_fees":           rule.OtherFees,
		"rule_category":        rule.RuleCategory,
		"repayment_method":     rule.RepaymentMethod,
		"created_at":           time.Unix(now, 0),
		"updated_at":           time.Unix(now, 0),
	}

	insertID, err := DB().Table("product_rules").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("创建产品规则失败: %v", err)
	}

	// 设置插入的ID
	rule.ID = int(insertID)

	return nil
}

// UpdateProductRule 更新产品规则
func (s *ProductRulesService) UpdateProductRule(id int, updates map[string]interface{}) error {
	if id <= 0 {
		return fmt.Errorf("产品规则ID无效")
	}

	// 设置更新时间
	updates["updated_at"] = time.Now()

	_, err := DB().Table("product_rules").Where("id", id).Data(updates).Update()
	if err != nil {
		return fmt.Errorf("更新产品规则失败: %v", err)
	}

	return nil
}

// DeleteProductRule 删除产品规则
func (s *ProductRulesService) DeleteProductRule(id int) error {
	if id <= 0 {
		return fmt.Errorf("产品规则ID无效")
	}

	_, err := DB().Table("product_rules").Where("id", id).Delete()
	if err != nil {
		return fmt.Errorf("删除产品规则失败: %v", err)
	}

	return nil
}

// ValidateProductRule 验证产品规则的完整性
func (s *ProductRulesService) ValidateProductRule(rule *ProductRules) error {
	if rule == nil {
		return fmt.Errorf("产品规则对象不能为空")
	}

	if rule.RuleName == "" {
		return fmt.Errorf("规则名称不能为空")
	}

	if rule.LoanAmount <= 0 {
		return fmt.Errorf("贷款金额必须大于0")
	}

	if rule.LoanPeriod <= 0 {
		return fmt.Errorf("贷款期限必须大于0")
	}

	if rule.TotalPeriods <= 0 {
		return fmt.Errorf("总期数必须大于0")
	}

	if rule.AnnualInterestRate < 0 {
		return fmt.Errorf("年利率不能为负数")
	}

	if rule.GuaranteeFee < 0 {
		return fmt.Errorf("担保费不能为负数")
	}

	if rule.OtherFees < 0 {
		return fmt.Errorf("其他费用不能为负数")
	}

	if rule.RuleCategory != "新用户" && rule.RuleCategory != "复购用户" {
		return fmt.Errorf("规则类别必须是'新用户'或'复购用户'")
	}

	if rule.RepaymentMethod != "前置付款" && rule.RepaymentMethod != "后置付款" {
		return fmt.Errorf("还款方式必须是'前置付款'或'后置付款'")
	}

	return nil
}
