<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { IconRefresh, IconSearch, IconUserAdd } from '@arco-design/web-vue/es/icon';
import AllocationCollection from '@/views/ordermanagement/components/allocationCollection/index.vue'
const allocationCollectionRef = ref<any>()
import Withhold from '@/views/ordermanagement/components/withhold/index.vue'
const withholdRef = ref<any>()
import recordCollectionForm from '@/views/ordermanagement/components/recordCollection/form.vue'
const recordCollectionFormRef = ref<any>()
import recordCollectionList from '@/views/ordermanagement/components/recordCollection/list.vue'
import { listOverdueBills } from '@/api/ordermanagement';
const recordCollectionListRef = ref<any>()
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getChannelList } from '@/api/channel';
const $router  = useRouter();
const queryFormRef = ref();
const queryForm = reactive({
  channel_id: '',
  user_name: '',
  user_mobile: '',
  order_no: '',
  channel_name: '',
  is_distribute_collection: '',
  collection_assignee_name: '',
  overdue_date_start: dayjs().format('YYYY-MM-DD'),
  overdue_date_end: dayjs().format('YYYY-MM-DD'),
  collection_time_start: '',
  collection_time_end: '',
})
const overdue_date = ref([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
const collection_time = ref();
const handleOverdueDateChange = (date: any) => {
  if(date && date.length > 0) {
    queryForm.overdue_date_start = date[0]
    queryForm.overdue_date_end = date[1]
  }else{
    queryForm.overdue_date_start = ''
    queryForm.overdue_date_end = ''
  }

}
const handleCollectionTimeChange = (date: any) => {
  if(date && date.length > 0) {
    queryForm.collection_time_start = date[0]
    queryForm.collection_time_end = date[1]
  }else{
    queryForm.collection_time_start = ''
    queryForm.collection_time_end = ''
  }
}
const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  listOverdueBills({
    ...queryForm,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then(res => {
    dataSource.value = res.data
  }).finally(() => {
    loading.value = false;
  })
}
const dictChannels = ref<any>([]);
onMounted(() => {
  fetchData()
  getChannelList({ page: 1, pageSize: 1000 }).then(res => {
    dictChannels.value = res.data;
  })
})

const columns = [
  {
    title: 'No',
    dataIndex: 'no',
  },
  {
    title: '订单编号',
    dataIndex: 'order_no',
    width: 220,
  },
  {
    title: '渠道来源',
    dataIndex: 'channel_name',
    width: 100,
  },
  {
    title: '催收人',
    dataIndex: 'collection_assignee_name',
    width: 100,
  },
  {
    title: '姓名/手机号',
    width: 180,
    render: ({ record }: { record:any }) => {
      return record.user_name + '/' + record.user_mobile
    }
  },
  {
    title: '身份证',
    dataIndex: 'id_card',
    width: 180,
  },
  {
    title: '期数',
    dataIndex: 'periods',
    width: 100,
  },
  {
    title: '已付金额',
    dataIndex: 'paid_amount',
    width: 100,
  },
  {
    title: '逾期日期/天数',
    dataIndex: 'overdue_date',
    width: 180,
  },
  {
    title: '成本',
    dataIndex: 'due_principal',
    width: 100,
  },
  {
    title: '差值',
    dataIndex: 'diff_amount',
    width: 100,
  },
  {
    title: '审核',
    dataIndex: 'sales_assignee_name',
    width: 100,
  },
  {
    title: '下单时间',
    dataIndex: 'created_at',
    width: 180,
  },
  {
    title: '最近登录时间',
    dataIndex: 'last_login_info',
    width: 180,
  },
  {
    title: '操作',
    slotName: 'action',
    width: 300,
    fixed: 'right',
  }
];
const dataSource = ref([{}])
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
}
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  overdue_date.value = [];
  collection_time.value = '';
  fetchData();
}

// 分配催收
const handleAllocationCollection = () => {
  if (selectedKeys.value.length === 0) {
    ElMessage.error('请选择要分配催收的账单')
    return
  }
  allocationCollectionRef.value.handleShow(selectedKeys.value)
}
// 批量代扣
const handleWithhold = () => {
  withholdRef.value.handleShow()
}
// 记录催收
const handleRecordCollection = (record: any) => {
  recordCollectionFormRef.value.handleShow(record.id)
}
// 查看催收记录
const handleRecordCollectionList = (record: any) => {
  recordCollectionListRef.value.handleShow(record.id)
}
const handleToDetail =  (record: any) => {
  $router.push({
    path: `/ordermanagement/Detail/${record.order_id}`,
    query: {
      orderNo: record.order_no,
      uid:  record.user_id,
    }
  })
}
</script>

<template>
  <div class="container">
    <a-card title="逾期账单" :bordered="false">
      <!-- 逾期账单表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch">
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item field="user_name" label="客户姓名">
                <a-input allow-clear v-model="queryForm.user_name" placeholder="客户姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="user_mobile" label="客户手机号">
                <a-input allow-clear v-model="queryForm.user_mobile" placeholder="客户手机号"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="order_no" label="订单编号">
                <a-input allow-clear v-model="queryForm.order_no" placeholder="订单编号"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="channel_id" label="渠道来源">
                <a-select allow-clear v-model="queryForm.channel_id" placeholder="渠道来源">
                  <a-option v-for="item in dictChannels" :key="item.id" :value="item.id">{{ item.channel_name }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="is_distribute_collection" label="是否分配催收">
                <a-select allow-clear v-model="queryForm.is_distribute_collection" placeholder="是否分配催收">
                  <a-option :value="1">是</a-option>
                  <a-option :value="0">否</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="collection_assignee_name" label="催收姓名">
                <a-input allow-clear v-model="queryForm.collection_assignee_name" placeholder="催收姓名"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="逾期时间">
                <a-range-picker allow-clear v-model="overdue_date" placeholder="逾期时间" @change="handleOverdueDateChange"/>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="最近催收时间">
                <a-range-picker show-time format="YYYY-MM-DD HH:mm:ss" :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }" allow-clear v-model="collection_time" placeholder="最近催收时间" @change="handleCollectionTimeChange"/>
              </a-form-item>
            </a-col>
            <a-col :md="24" :sm="24">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
                <a-button type="primary" status="success" @click="handleAllocationCollection">
                  <template #icon>
                    <icon-user-add />
                  </template>
                  分配催收
                </a-button>
<!--                <a-button type="primary" status="success">
                  <template #icon>
                    <icon-user-add />
                  </template>
                  二次分配催收
                </a-button>
                <a-button type="primary" status="success">
                  <template #icon>
                    <icon-upload />
                  </template>
                  案件回收
                </a-button>
                <a-button type="primary" status="success">
                  <template #icon>
                    <icon-export />
                  </template>
                  导出
                </a-button>
                <a-button type="primary" status="success">
                  <template #icon>
                    <icon-upload />
                  </template>
                  案件导入回收
                </a-button>-->
              </a-space>
            </a-col>
          </a-row>
        </a-form>

      </div>

    </a-card>
    <!-- 到期账单列表 -->
    <a-card class="table-card" title="到期账单列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="false"
        size="medium"
        row-key="id"
        :scroll="{ y: '100%' }"
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
        <template #action="{record}">
          <a-button type="text" @click="handleToDetail(record)">处理</a-button>
          <a-button type="text" @click="handleRecordCollectionList(record)">查看催收</a-button>
          <a-button type="text" @click="handleRecordCollection(record)">记录催收</a-button>
        </template>
      </a-table>
    </a-card>
    <!-- 分配催收 弹窗 -->
    <allocation-collection ref="allocationCollectionRef" @upload="fetchData"/>
    <!-- 批量代扣 弹窗 -->
    <withhold ref="withholdRef" @upload="fetchData"/>
    <!-- 记录催收 弹窗 -->
    <record-collection-form ref="recordCollectionFormRef" @upload="fetchData"/>
    <!-- 查看催收记录 弹窗 -->
    <record-collection-list ref="recordCollectionListRef" @upload="fetchData"/>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}
.stat-grid{
  .arco-col{
    height: 80px;
  }
  .stat-item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    .stat-title{
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }
    .stat-number{
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top:  10px;
      font-weight: bold;
    }
  }
}
.due-form{
  margin-top: 20px;
}
.table-card{
  margin-top: 10px;
}
</style>