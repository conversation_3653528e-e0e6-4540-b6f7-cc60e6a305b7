{"level": "dev.info", "ts": "[2025-08-20 10:46:22.600]", "caller": "log/middleware.go:168", "msg": "HTTP请求", "request_id": "185d59a21f3b5c28fa774cd6", "method": "POST", "url": "/business/payment/manager/calculateRepaymentSchedule", "query": "", "ip": "************", "user_agent": "Apifox/1.0.0 (https://apifox.com)", "status_code": 200, "response_time": 0.0026111, "request_size": 185, "response_size": 959}