<!-- 批量代扣 弹窗 select 选择‘资管代扣’|’担保代扣‘-->
<script setup lang="ts">
import { ref } from 'vue';
const visible = ref(false);
const value = ref('');
const handleShow = () => {
  visible.value = true;
}
const handleOk = () => {
  visible.value = false;
}
const handleCancel = () => {
  visible.value = false;
}
defineExpose({
  handleShow,
})
</script>

<template>
  <a-modal
    title="批量代扣"
    v-model:visible="visible"
    :width="400"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-select v-model:value="value" placeholder="请选择">
      <a-option value="1">资管代扣</a-option>
      <a-option value="2">担保代扣</a-option>
    </a-select>
  </a-modal>
</template>

<style scoped lang="less">

</style>