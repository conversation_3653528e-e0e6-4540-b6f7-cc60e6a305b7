openapi: 3.0.0
info:
  title: FinCore 统计管理 API
  description: FinCore系统统计管理模块的完整API接口文档，包含首页统计、渠道统计、趋势统计等功能
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8108
    description: 统计API服务器（开发环境）
  - url: https://api.fincore.com
    description: 统计API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    # 首页统计相关schemas
    HomeStatisticsData:
      type: object
      description: 首页统计数据
      properties:
        disbursement_amount:
          type: number
          format: float
          description: 放款金额（本金+利息+担保）
          example: 150000.00
        disbursement_principal_amount:
          type: number
          format: float
          description: 放款本金
          example: 120000.00
        disbursement_customer_count:
          type: integer
          description: 放款客户数
          example: 25
        disbursement_order_count:
          type: integer
          description: 放款订单数
          example: 30
        due_amount:
          type: number
          format: float
          description: 到期金额（本金+利息+担保）
          example: 120000.00
        due_repayment_amount:
          type: number
          format: float
          description: 到期回款总额（本息+担保）
          example: 108000.00
        due_repayment_rate:
          type: number
          format: float
          description: 到期回款率（回款总额/到期金额）
          example: 90.00
        due_funds_recovery_rate:
          type: number
          format: float
          description: 到期资金回收率（回款总额/本金）
          example: 85.50
        overdue_customer_count:
          type: integer
          description: 逾期客户数
          example: 5
        overdue_amount:
          type: number
          format: float
          description: 逾期总额（本息+担保）
          example: 12000.00
      required:
        - disbursement_amount
        - disbursement_principal_amount
        - disbursement_customer_count
        - disbursement_order_count
        - due_amount
        - due_repayment_amount
        - due_repayment_rate
        - due_funds_recovery_rate
        - overdue_customer_count
        - overdue_amount

    HomeStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/HomeStatisticsData'

    # 趋势统计相关schemas
    TrendDataPoint:
      type: object
      description: 趋势数据点
      properties:
        date:
          type: string
          description: 日期（YYYY-MM-DD格式）
          example: "2024-08-12"
        disbursement_amount:
          type: number
          format: float
          description: 放款金额
          example: 50000.00
        repayment_amount:
          type: number
          format: float
          description: 回款金额
          example: 45000.00
        due_amount:
          type: number
          format: float
          description: 到期金额
          example: 48000.00
        repayment_rate:
          type: number
          format: float
          description: 回款率（百分比）
          example: 90.00
        registration_count:
          type: integer
          description: 注册量
          example: 25
      required:
        - date
        - disbursement_amount
        - repayment_amount
        - due_amount
        - repayment_rate
        - registration_count

    TrendStatisticsData:
      type: object
      description: 趋势统计数据
      properties:
        trend_data:
          type: array
          description: 趋势数据列表
          items:
            $ref: '#/components/schemas/TrendDataPoint'
      required:
        - trend_data

    TrendStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/TrendStatisticsData'

    # 渠道统计相关schemas
    ChannelStatisticsItem:
      type: object
      description: 渠道统计数据项
      properties:
        id:
          type: integer
          description: 统计记录ID
          example: 1
        channel_id:
          type: integer
          description: 渠道ID
          example: 3
        channel_name:
          type: string
          description: 渠道名称
          example: "test"
        channel_code:
          type: string
          description: 渠道编码
          example: "NZKMYUHLPO"
        new_customer_reg_num:
          type: integer
          description: 新用户注册数
          example: 5
        real_name_num:
          type: integer
          description: 实名认证数
          example: 4
        number_of_transactions:
          type: integer
          description: 交易笔数
          example: 2
        created_at:
          type: string
          description: 统计时间（记录创建时间）
          example: "2024-08-04 12:00:00"
      required:
        - id
        - channel_id
        - channel_name
        - channel_code
        - new_customer_reg_num
        - real_name_num
        - number_of_transactions
        - created_at

    ChannelStatisticsPaginationResponse:
      type: object
      description: 渠道统计分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 5
        page:
          type: integer
          description: 当前页码
          example: 1
        page_size:
          type: integer
          description: 每页数量
          example: 10
        total_pages:
          type: integer
          description: 总页数
          example: 1
        has_next:
          type: boolean
          description: 是否有下一页
          example: false
        has_prev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 渠道统计数据列表
          items:
            $ref: '#/components/schemas/ChannelStatisticsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    ChannelStatisticsListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChannelStatisticsPaginationResponse'

    # 收入明细相关schemas
    IncomeStatistics:
      type: object
      description: 收入统计数据
      properties:
        total_income:
          type: number
          format: float
          description: 收款合计
          example: 1163.21
        total_refund:
          type: number
          format: float
          description: 退款合计
          example: 60.14
        due_income:
          type: number
          format: float
          description: 到期收款合计
          example: 0.00
        overdue_income:
          type: number
          format: float
          description: 逾期收款合计
          example: 65.83
        early_income:
          type: number
          format: float
          description: 提前处理收款合计
          example: 999.06
        total_customers:
          type: integer
          description: 收款人数合计
          example: 3
        total_orders:
          type: integer
          description: 收款订单数合计
          example: 19
      required:
        - total_income
        - total_refund
        - due_income
        - overdue_income
        - early_income
        - total_customers
        - total_orders

    IncomeDetailsItem:
      type: object
      description: 收入明细数据项
      properties:
        id:
          type: integer
          description: 流水记录ID
          example: 226
        transaction_no:
          type: string
          description: 流水号
          example: "D1754980939743087000190908"
        order_no:
          type: string
          description: 订单编号
          example: "LO20250812OY6SMGWH"
        user_name:
          type: string
          description: 用户姓名
          example: "尹渡"
        mobile:
          type: string
          description: 手机号
          example: "19194253882"
        type:
          type: string
          description: 流水类型
          enum: [DISBURSEMENT, REPAYMENT, WITHHOLD, MANUAL_WITHHOLD, PARTIAL_OFFLINE_REPAYMENT, REFUND]
          example: "REPAYMENT"
        fund_type_text:
          type: string
          description: 款项类型
          enum: [收款, 退款]
          example: "收款"
        payment_method_text:
          type: string
          description: 收款方式
          enum: [资管支付, 资管代扣, 担保支付, 担保代扣, 线下支付, 其他]
          example: "担保支付"
        amount:
          type: string
          description: 收款金额
          example: "50.00"
        period_number:
          type: integer
          description: 期数
          example: 1
        completed_at:
          type: string
          description: 收款时间
          example: "2025-08-12 14:42:24"
        due_date:
          type: string
          description: 账单到期时间
          example: "2025-08-23"
        withhold_type:
          type: string
          description: 代扣类型
          enum: [ASSET, GUARANTEE]
          example: "ASSET"
        offline_payment_channel_detail:
          type: string
          description: 线下支付渠道详情
          example: "支付宝支付（线下）"
      required:
        - id
        - transaction_no
        - order_no
        - user_name
        - mobile
        - type
        - fund_type_text
        - payment_method_text
        - amount
        - completed_at

    IncomeDetailsPaginationResponse:
      type: object
      description: 收入明细分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 111
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 10
        totalPages:
          type: integer
          description: 总页数
          example: 12
        hasNext:
          type: boolean
          description: 是否有下一页
          example: true
        hasPrev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 收入明细数据列表
          items:
            $ref: '#/components/schemas/IncomeDetailsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    IncomeDetailsData:
      type: object
      description: 收入明细响应数据
      properties:
        statistics:
          $ref: '#/components/schemas/IncomeStatistics'
        list:
          $ref: '#/components/schemas/IncomeDetailsPaginationResponse'
      required:
        - statistics
        - list

    IncomeDetailsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/IncomeDetailsData'

    # 渠道到期统计相关schemas
    ChannelDueStatisticsSummary:
      type: object
      description: 渠道到期统计汇总数据
      properties:
        total_due_bills:
          type: integer
          description: 到期账单数
          example: 38
        new_user_bills:
          type: integer
          description: 新用户账单数
          example: 0
        old_user_bills:
          type: integer
          description: 老用户账单数
          example: 38
        total_due_amount:
          type: string
          description: 到期金额
          example: "1079.10"
        new_user_due_amount:
          type: string
          description: 新用户到期金额
          example: "0.00"
        old_user_due_amount:
          type: string
          description: 老用户到期金额
          example: "1079.10"
        total_repayment_rate:
          type: string
          description: 金额回款率（百分比）
          example: "82.44"
        new_user_repayment_rate:
          type: string
          description: 新用户金额回款率（百分比）
          example: "0.00"
        old_user_repayment_rate:
          type: string
          description: 老用户金额回款率（百分比）
          example: "82.44"
      required:
        - total_due_bills
        - new_user_bills
        - old_user_bills
        - total_due_amount
        - new_user_due_amount
        - old_user_due_amount
        - total_repayment_rate
        - new_user_repayment_rate
        - old_user_repayment_rate

    ChannelDueStatisticsItem:
      type: object
      description: 渠道到期统计列表项
      properties:
        channel_name:
          type: string
          description: 渠道名称
          example: "qd0806_2"
        due_bills_count:
          type: integer
          description: 到期账单数
          example: 38
        new_user_bills:
          type: integer
          description: 新用户账单数
          example: 0
        old_user_bills:
          type: integer
          description: 老用户账单数
          example: 38
        due_amount:
          type: string
          description: 到期金额
          example: "1079.10"
        new_user_due_amount:
          type: string
          description: 新用户到期金额
          example: "0.00"
        old_user_due_amount:
          type: string
          description: 老用户到期金额
          example: "1079.10"
        avg_bill_amount:
          type: string
          description: 账单件均（到期金额/到期账单数）
          example: "28.40"
        repayment_rate:
          type: string
          description: 金额回款率（百分比）
          example: "82.44"
        new_user_repayment_rate:
          type: string
          description: 新用户金额回款率（百分比）
          example: "0.00"
        old_user_repayment_rate:
          type: string
          description: 老用户金额回款率（百分比）
          example: "82.44"
        repayment_users:
          type: integer
          description: 还款人数（包括主动还款、系统代扣和人工代扣场景）
          example: 1
        repeat_purchase_users:
          type: integer
          description: 还款复购人数（还款且为复购的用户数）
          example: 1
        repeat_purchase_rate:
          type: string
          description: 复购率（复购人数/总下单人数×100%）
          example: "100.00"
      required:
        - channel_name
        - due_bills_count
        - new_user_bills
        - old_user_bills
        - due_amount
        - new_user_due_amount
        - old_user_due_amount
        - avg_bill_amount
        - repayment_rate
        - new_user_repayment_rate
        - old_user_repayment_rate
        - repayment_users
        - repeat_purchase_users
        - repeat_purchase_rate

    ChannelDueStatisticsPaginationResponse:
      type: object
      description: 渠道到期统计分页响应
      properties:
        total:
          type: integer
          description: 总渠道数（包括没有订单的启用渠道）
          example: 3
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 5
        totalPages:
          type: integer
          description: 总页数
          example: 1
        hasNext:
          type: boolean
          description: 是否有下一页
          example: false
        hasPrev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 渠道到期统计完整列表数据（包含所有启用渠道，无订单渠道数据为0）
          items:
            $ref: '#/components/schemas/ChannelDueStatisticsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    ChannelDueStatisticsData:
      type: object
      description: 渠道到期统计响应数据
      properties:
        statistics:
          $ref: '#/components/schemas/ChannelDueStatisticsSummary'
        pagination:
          $ref: '#/components/schemas/ChannelDueStatisticsPaginationResponse'
      required:
        - statistics
        - pagination

    ChannelDueStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChannelDueStatisticsData'

    # 支出明细相关schemas
    ExpenseStatistics:
      type: object
      description: 支出统计数据
      properties:
        total_expense:
          type: number
          format: float
          description: 支出总额（放款金额之和）
          example: 1000000.50
        new_user_expense:
          type: number
          format: float
          description: 新用户支出总额
          example: 300000.25
        old_user_expense:
          type: number
          format: float
          description: 旧用户支出总额
          example: 700000.25
        today_expense:
          type: number
          format: float
          description: 今日支出（今日放款的金额之和）
          example: 50000.00
        expense_count:
          type: integer
          description: 支出笔数（放款的订单数）
          example: 150
        expense_orders:
          type: integer
          description: 支出订单数（放款的订单数）
          example: 150
        expense_users:
          type: integer
          description: 支出人数（放款订单相关客户数）
          example: 120
      required:
        - total_expense
        - new_user_expense
        - old_user_expense
        - today_expense
        - expense_count
        - expense_orders
        - expense_users

    ExpenseDetailsItem:
      type: object
      description: 支出明细数据项
      properties:
        order_no:
          type: string
          description: 订单编号
          example: "ORD20240101001"
        amount:
          type: string
          description: 金额（放款金额，格式化为两位小数）
          example: "5000.00"
        user_name:
          type: string
          description: 用户姓名
          example: "张三"
        mobile:
          type: string
          description: 手机号
          example: "***********"
        bank_card_no:
          type: string
          description: 支出账户（银行卡号）
          example: "6222021234567890123"
        payment_method:
          type: string
          description: 支付方式（固定为"统统付小贷打款"）
          example: "统统付小贷打款"
        disbursed_at:
          type: string
          description: 时间（放款时间）
          example: "2024-01-01 10:30:00"
      required:
        - order_no
        - amount
        - user_name
        - mobile
        - bank_card_no
        - payment_method
        - disbursed_at

    ExpenseDetailsPaginationResponse:
      type: object
      description: 支出明细分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 150
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 10
        totalPages:
          type: integer
          description: 总页数
          example: 15
        hasNext:
          type: boolean
          description: 是否有下一页
          example: true
        hasPrev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 支出明细数据列表
          items:
            $ref: '#/components/schemas/ExpenseDetailsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    ExpenseDetailsData:
      type: object
      description: 支出明细响应数据
      properties:
        statistics:
          $ref: '#/components/schemas/ExpenseStatistics'
        list:
          $ref: '#/components/schemas/ExpenseDetailsPaginationResponse'
      required:
        - statistics
        - list

    ExpenseDetailsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ExpenseDetailsData'

paths:
  # 首页统计接口
  /business/statistics/statisticscontroller/getHomeStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取首页数据统计
      description: |
        获取首页展示的核心统计数据，包括放款、回款、逾期等关键业务指标。

        **业务说明：**
        - 当date_begin和date_end参数为空时，返回累计统计数据
        - 当指定时间范围时，返回该时间范围内的统计数据
        - 开始时间不能大于结束时间
        - 逾期统计：返回截止到查询时间的累计逾期数据
        - 所有金额计算使用高精度decimal库确保准确性
        - 使用并行查询优化性能

        **数据逻辑：**
        - 放款金额：已放款订单的总金额（本金+利息+担保）
        - 放款本金：已放款订单的本金总额
        - 放款客户数：有放款记录的唯一客户数
        - 放款订单数：已放款的订单总数
        - 到期金额：应还款账单的总金额（本金+利息+担保）
        - 到期回款总额：已支付账单的总金额
        - 到期回款率：回款总额/到期金额×100%
        - 到期资金回收率：回款总额/放款本金×100%
        - 逾期客户数：有逾期账单的唯一客户数
        - 逾期总额：逾期账单的未还金额

        **权限要求：**
        - 需要登录用户权限
        - 需要首页统计查看权限
      parameters:
        - name: date_begin
          in: query
          description: 开始日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-01"
        - name: date_end
          in: query
          description: 结束日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-12"
      responses:
        '200':
          description: 获取首页统计数据成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HomeStatisticsResponse'
              examples:
                with_date_range:
                  summary: 指定时间范围统计数据响应示例
                  value:
                    code: 0
                    message: "获取首页数据统计成功"
                    data:
                      disbursement_amount: 150000.00
                      disbursement_principal_amount: 120000.00
                      disbursement_customer_count: 25
                      disbursement_order_count: 30
                      due_amount: 120000.00
                      due_repayment_amount: 108000.00
                      due_repayment_rate: 90.00
                      due_funds_recovery_rate: 85.50
                      overdue_customer_count: 5
                      overdue_amount: 12000.00
                    exdata: null
                    token: ""
                    time: 1754292247
                without_date:
                  summary: 累计统计数据响应示例
                  value:
                    code: 0
                    message: "获取首页数据统计成功"
                    data:
                      disbursement_amount: 2500000.00
                      disbursement_principal_amount: 2000000.00
                      disbursement_customer_count: 450
                      disbursement_order_count: 520
                      due_amount: 2200000.00
                      due_repayment_amount: 1980000.00
                      due_repayment_rate: 90.00
                      due_funds_recovery_rate: 85.50
                      overdue_customer_count: 35
                      overdue_amount: 220000.00
                    exdata: null
                    token: ""
                    time: 1754292247
                no_data:
                  summary: 无数据响应示例
                  value:
                    code: 0
                    message: "获取首页数据统计成功"
                    data:
                      disbursement_amount: 0.00
                      disbursement_principal_amount: 0.00
                      disbursement_customer_count: 0
                      disbursement_order_count: 0
                      due_amount: 0.00
                      due_repayment_amount: 0.00
                      due_repayment_rate: 0.00
                      due_funds_recovery_rate: 0.00
                      overdue_customer_count: 0
                      overdue_amount: 0.00
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date_format:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_date_range:
                  summary: 时间范围错误
                  value:
                    code: 1
                    message: "开始时间不能大于结束时间"
                    data: null
                    exdata: null
                    token: ""
                    time: 1754292247
                date_parse_error:
                  summary: 日期解析错误
                  value:
                    code: 1
                    message: "开始时间格式错误"
                    data: null
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取首页数据统计失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247
                calculation_error:
                  summary: 计算错误
                  value:
                    code: 1
                    message: "获取首页数据统计失败"
                    data: "统计数据计算失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 趋势统计接口
  /business/statistics/statisticscontroller/getTrendStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取首页统计趋势数据
      description: |
        获取首页展示的趋势统计数据，包括放款、回款、回款率、注册量的时间序列数据。

        **业务说明：**
        - 支持查询近7日或近30日的趋势数据
        - 每天一个数据点，返回完整的时间序列
        - 包含4个核心趋势：放款金额、回款金额、回款率、注册量
        - 使用并行查询优化性能
        - 所有金额计算使用高精度decimal库确保准确性
        - 自动填充缺失日期的数据点（值为0）

        **数据逻辑：**
        - 放款金额：当日成功放款的订单总金额
        - 回款金额：当日已支付的账单总金额
        - 回款率：当日回款金额/放款金额×100%
        - 注册量：当日新注册的用户数量

        **权限要求：**
        - 需要登录用户权限
        - 需要首页统计查看权限
      parameters:
        - name: days
          in: query
          description: 查询天数，支持7日或30日
          required: true
          schema:
            type: integer
            enum: [7, 30]
            example: 7
      responses:
        '200':
          description: 获取趋势统计数据成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendStatisticsResponse'
              examples:
                seven_days:
                  summary: 近7日趋势数据响应示例
                  value:
                    code: 0
                    message: "获取趋势统计数据成功"
                    data:
                      trend_data:
                        - date: "2024-08-06"
                          disbursement_amount: 45000.00
                          repayment_amount: 40000.00
                          due_amount: 42000.00
                          repayment_rate: 88.89
                          registration_count: 20
                        - date: "2024-08-07"
                          disbursement_amount: 50000.00
                          repayment_amount: 45000.00
                          due_amount: 48000.00
                          repayment_rate: 90.00
                          registration_count: 25
                        - date: "2024-08-08"
                          disbursement_amount: 55000.00
                          repayment_amount: 50000.00
                          due_amount: 52000.00
                          repayment_rate: 90.91
                          registration_count: 30
                        - date: "2024-08-09"
                          disbursement_amount: 48000.00
                          repayment_amount: 43000.00
                          due_amount: 46000.00
                          repayment_rate: 89.58
                          registration_count: 22
                        - date: "2024-08-10"
                          disbursement_amount: 52000.00
                          repayment_amount: 47000.00
                          due_amount: 49000.00
                          repayment_rate: 90.38
                          registration_count: 28
                        - date: "2024-08-11"
                          disbursement_amount: 49000.00
                          repayment_amount: 44000.00
                          due_amount: 47000.00
                          repayment_rate: 89.80
                          registration_count: 24
                        - date: "2024-08-12"
                          disbursement_amount: 51000.00
                          repayment_amount: 46000.00
                          due_amount: 48000.00
                          repayment_rate: 90.20
                          registration_count: 26
                    exdata: null
                    token: ""
                    time: 1754292247
                no_data:
                  summary: 无数据响应示例
                  value:
                    code: 0
                    message: "获取趋势统计数据成功"
                    data:
                      trend_data:
                        - date: "2024-08-06"
                          disbursement_amount: 0.00
                          repayment_amount: 0.00
                          due_amount: 0.00
                          repayment_rate: 0.00
                          registration_count: 0
                        - date: "2024-08-07"
                          disbursement_amount: 0.00
                          repayment_amount: 0.00
                          due_amount: 0.00
                          repayment_rate: 0.00
                          registration_count: 0
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_days:
                  summary: 天数参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "天数参数必须是7或30"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取趋势统计数据失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247
                calculation_error:
                  summary: 计算错误
                  value:
                    code: 1
                    message: "获取趋势统计数据失败"
                    data: "趋势数据计算失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 渠道统计接口
  /business/statistics/statisticscontroller/getChannelStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取渠道统计列表
      description: |
        获取渠道维度的统计数据列表，支持分页和日期筛选。

        **业务说明：**
        - 查询channel_statistics表数据并关联channel表获取渠道信息
        - 支持按日期筛选统计数据
        - 返回数据包含：渠道信息、新用户注册数、实名认证数、交易笔数等
        - 按统计时间倒序排列

        **权限要求：**
        - 需要登录用户权限
        - 需要渠道统计查看权限
      parameters:
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: date
          in: query
          description: 统计日期筛选（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-04"
      responses:
        '200':
          description: 获取渠道统计列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelStatisticsListResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 3
                      page: 1
                      pageSize: 10
                      totalPages: 1
                      hasNext: false
                      hasPrev: false
                      data:
                        - id: 3
                          channel_id: 3
                          channel_name: "test"
                          channel_code: "NZKMYUHLPO"
                          new_customer_reg_num: 5
                          real_name_num: 4
                          number_of_transactions: 2
                          created_at: "2024-08-04 12:00:00"
                        - id: 2
                          channel_id: 2
                          channel_name: "公众号"
                          channel_code: "GZH8F5N3P7"
                          new_customer_reg_num: 15
                          real_name_num: 12
                          number_of_transactions: 8
                          created_at: "2024-08-04 11:00:00"
                        - id: 1
                          channel_id: 1
                          channel_name: "fincore"
                          channel_code: "HS2K9X7Q1M"
                          new_customer_reg_num: 10
                          real_name_num: 8
                          number_of_transactions: 5
                          created_at: "2024-08-04 10:00:00"
                    exdata: null
                    token: ""
                    time: 1754292247
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 0
                      page: 1
                      pageSize: 10
                      totalPages: 0
                      hasNext: false
                      hasPrev: false
                      data: []
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_page:
                  summary: 页码参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取渠道统计列表失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 收入明细接口
  /business/statistics/statisticscontroller/getIncomeDetails:
    get:
      tags:
        - 统计管理
      summary: 获取收入明细
      description: |
        获取收入明细统计和列表数据，支持多种筛选条件和分页查询。

        **业务说明：**
        - 以流水表为主表，关联账单、订单、用户信息
        - 流水状态限制为处理成功（status = 2）
        - 统计数据和列表数据使用相同的筛选条件，确保数据一致性
        - 支持多维度筛选：订单编号、用户信息、款项类型、收款方式、收款状态、时间范围等
        - 使用并行查询优化性能

        **参数枚举值说明：**
        - fund_type: 0=收款, 1=退款
        - payment_method: 0=资管支付, 1=资管代扣, 2=担保支付, 3=担保代扣, 4=支付宝支付（线下）, 5=微信支付（线下）, 6=银行卡支付（线下）, 7=信用卡支付（线下）
        - payment_status: 0=提前收款, 1=到期收款, 2=逾期收款

        **收款方式判断逻辑：**
        - 线下支付：offline_payment_channel_detail字段不为空
        - 资管支付：withhold_type = 'ASSET' AND type = 'REPAYMENT'
        - 资管代扣：withhold_type = 'ASSET' AND type = 'WITHHOLD'
        - 担保支付：withhold_type = 'GUARANTEE' AND type = 'REPAYMENT'
        - 担保代扣：withhold_type = 'GUARANTEE' AND type = 'WITHHOLD'

        **款项类型判断逻辑：**
        - 收款：type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')
        - 退款：type = 'REFUND'

        **收款状态判断逻辑：**
        - 提前收款：收款时间 < 账单到期时间
        - 到期收款：收款时间 = 账单到期时间（按日期比较）
        - 逾期收款：收款时间 > 账单到期时间

        **统计数据说明：**
        - 收款合计：所有收款类型流水的金额总和
        - 退款合计：所有退款类型流水的金额总和
        - 到期收款合计：收款时间等于账单到期时间的流水金额总和
        - 逾期收款合计：收款时间大于账单到期时间的流水金额总和
        - 提前处理收款合计：收款时间小于账单到期时间的流水金额总和
        - 收款人数合计：有收款记录的唯一用户数
        - 收款订单数合计：有收款记录的唯一订单数

        **权限要求：**
        - 需要登录用户权限
        - 需要收入明细查看权限
      parameters:
        - name: order_no
          in: query
          description: 订单编号（模糊匹配）
          required: false
          schema:
            type: string
            example: "LO20250812"
        - name: user_name
          in: query
          description: 用户姓名（模糊匹配）
          required: false
          schema:
            type: string
            example: "张三"
        - name: mobile
          in: query
          description: 手机号（模糊匹配，需符合手机号格式）
          required: false
          schema:
            type: string
            pattern: '^1[3-9]\\d{9}$'
            example: "13800138000"
        - name: fund_type
          in: query
          description: 款项类型，值只能为 0 - 1 的整数
          required: false
          schema:
            type: integer
            enum: [0, 1]
            example: 0
          x-enum-descriptions:
            - "0: 收款"
            - "1: 退款"
        - name: payment_method
          in: query
          description: 收款方式，值只能为 0 - 7 的整数
          required: false
          schema:
            type: integer
            enum: [0, 1, 2, 3, 4, 5, 6, 7]
            example: 2
          x-enum-descriptions:
            - "0: 资管支付"
            - "1: 资管代扣"
            - "2: 担保支付"
            - "3: 担保代扣"
            - "4: 支付宝支付（线下）"
            - "5: 微信支付（线下）"
            - "6: 银行卡支付（线下）"
            - "7: 信用卡支付（线下）"
        - name: payment_status
          in: query
          description: 收款状态，值只能为 0 - 2 的整数
          required: false
          schema:
            type: integer
            enum: [0, 1, 2]
            example: 0
          x-enum-descriptions:
            - "0: 提前收款"
            - "1: 到期收款"
            - "2: 逾期收款"
        - name: payment_time_start
          in: query
          description: 收款开始时间（YYYY-MM-DD格式）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-01"
        - name: payment_time_end
          in: query
          description: 收款结束时间（YYYY-MM-DD格式）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-31"
        - name: bill_time_start
          in: query
          description: 账单开始时间（YYYY-MM-DD格式）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-01"
        - name: bill_time_end
          in: query
          description: 账单结束时间（YYYY-MM-DD格式）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-31"
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
      responses:
        '200':
          description: 获取收入明细成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IncomeDetailsResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    message: "获取收入明细成功"
                    data:
                      statistics:
                        total_income: 1163.21
                        total_refund: 60.14
                        due_income: 0.00
                        overdue_income: 65.83
                        early_income: 999.06
                        total_customers: 3
                        total_orders: 19
                      list:
                        total: 111
                        page: 1
                        pageSize: 10
                        totalPages: 12
                        hasNext: true
                        hasPrev: false
                        data:
                          - id: 226
                            transaction_no: "D1754980939743087000190908"
                            order_no: "LO20250812OY6SMGWH"
                            user_name: "尹渡"
                            mobile: "19194253882"
                            type: "REPAYMENT"
                            fund_type_text: "收款"
                            payment_method_text: "担保支付"
                            amount: "50.00"
                            period_number: 1
                            completed_at: "2025-08-12 14:42:24"
                            due_date: "2025-08-23"
                            withhold_type: "GUARANTEE"
                            offline_payment_channel_detail: ""
                          - id: 225
                            transaction_no: "D1754980939743087000190907"
                            order_no: "LO20250812ABCDEFGH"
                            user_name: "李明"
                            mobile: "13800138000"
                            type: "WITHHOLD"
                            fund_type_text: "收款"
                            payment_method_text: "资管代扣"
                            amount: "100.00"
                            period_number: 2
                            completed_at: "2025-08-12 15:30:15"
                            due_date: "2025-08-12"
                            withhold_type: "ASSET"
                            offline_payment_channel_detail: ""
                    exdata: null
                    token: ""
                    time: 1754292247
                filtered:
                  summary: 筛选查询响应示例
                  value:
                    code: 0
                    message: "获取收入明细成功"
                    data:
                      statistics:
                        total_income: 50.00
                        total_refund: 0.00
                        due_income: 0.00
                        overdue_income: 0.00
                        early_income: 50.00
                        total_customers: 1
                        total_orders: 1
                      list:
                        total: 1
                        page: 1
                        pageSize: 5
                        totalPages: 1
                        hasNext: false
                        hasPrev: false
                        data:
                          - id: 226
                            transaction_no: "D1754980939743087000190908"
                            order_no: "LO20250812OY6SMGWH"
                            user_name: "尹渡"
                            mobile: "19194253882"
                            type: "REPAYMENT"
                            fund_type_text: "收款"
                            payment_method_text: "担保支付"
                            amount: "50.00"
                            period_number: 1
                            completed_at: "2025-08-12 14:42:24"
                            due_date: "2025-08-23"
                            withhold_type: "GUARANTEE"
                            offline_payment_channel_detail: ""
                    exdata: null
                    token: ""
                    time: 1754292247
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取收入明细成功"
                    data:
                      statistics:
                        total_income: 0.00
                        total_refund: 0.00
                        due_income: 0.00
                        overdue_income: 0.00
                        early_income: 0.00
                        total_customers: 0
                        total_orders: 0
                      list:
                        total: 0
                        page: 1
                        pageSize: 10
                        totalPages: 0
                        hasNext: false
                        hasPrev: false
                        data: []
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_mobile:
                  summary: 手机号格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "手机号格式错误"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_date_format:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_enum:
                  summary: 枚举值错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "款项类型值只能为 0 - 1 的整数"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_page:
                  summary: 分页参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取收入明细失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247
                query_error:
                  summary: 查询错误
                  value:
                    code: 1
                    message: "获取收入明细失败"
                    data: "查询收入明细列表失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 渠道到期统计接口
  /business/statistics/statisticscontroller/getChannelDueStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取渠道到期统计
      description: |
        获取渠道到期统计数据，包括统计汇总和分页列表，支持多种筛选条件。

        **重要特性：显示所有启用渠道**
        - 接口会显示所有启用状态的渠道，包括没有订单和账单的渠道
        - 没有业务数据的渠道统计值为0，但仍会在列表中显示
        - 分页基于渠道数量，而不是账单数量
        - 确保用户能看到所有渠道的完整状态

        **业务说明：**
        - 以渠道表为主表，使用LEFT JOIN关联查询订单和账单以及流水表获取相关数据
        - 显示所有启用的渠道，包括没有订单和账单的渠道（数据为0）
        - 使用拆分查询和并行执行优化性能，避免复杂JOIN查询
        - 统计项字段的值为列表相关的列之和
        - 已还金额包含减免金额在内（paid_amount + total_waive_amount）
        - 使用shopspringutils包进行精确的金额计算，避免浮点数精度问题

        **新老用户判断逻辑：**
        - 新用户：仅有一笔订单的用户
        - 老用户：有多笔订单的用户

        **回款率计算逻辑：**
        - 金额回款率 = (已还金额 + 减免金额) / 应还金额 × 100%
        - 使用shopspringutils.DivideAmountsWithDecimal进行精确计算

        **复购率计算逻辑：**
        - 复购率 = 复购用户数 / 下单用户数 × 100%
        - 还款复购人数：还款用户中订单数>1的用户数
        - 复购用户数：下单用户中订单数>1的用户数

        **还款人数统计：**
        - 包括主动还款、系统代扣和人工代扣场景
        - 通过流水表的type字段判断：REPAYMENT、WITHHOLD、PARTIAL_OFFLINE_REPAYMENT、MANUAL_WITHHOLD

        **渠道显示逻辑：**
        - 显示所有启用状态的渠道（channel_status = 1）
        - 没有订单的渠道也会显示，统计数据为0
        - 分页基于渠道数量，而不是账单数量

        **性能优化：**
        - 使用5个拆分查询并行执行，避免复杂JOIN
        - 响应时间从2-5秒优化到60-80ms
        - 创建了针对性的复合索引
        - 重构筛选条件逻辑，实现代码复用

        **权限要求：**
        - 需要登录用户权限
        - 需要渠道统计查看权限
      parameters:
        - name: channel_id
          in: query
          description: 渠道ID（可选）
          required: false
          schema:
            type: integer
            example: 2
        - name: due_date_start
          in: query
          description: 到期开始日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-01-01"
        - name: due_date_end
          in: query
          description: 到期结束日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-12-31"
        - name: period_number
          in: query
          description: 账单期数（可选）
          required: false
          schema:
            type: integer
            example: 1
        - name: is_new_user
          in: query
          description: 是否新用户（可选，0-老用户，1-新用户）
          required: false
          schema:
            type: integer
            enum: [0, 1]
            example: 0
          x-enum-descriptions:
            - "0: 老用户"
            - "1: 新用户"
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 5
      responses:
        '200':
          description: 获取渠道到期统计成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelDueStatisticsResponse'
              examples:
                success:
                  summary: 成功响应示例（包含有订单和无订单的渠道）
                  value:
                    code: 0
                    message: "获取渠道到期统计成功"
                    data:
                      statistics:
                        total_due_bills: 38
                        new_user_bills: 0
                        old_user_bills: 38
                        total_due_amount: "1079.10"
                        new_user_due_amount: "0.00"
                        old_user_due_amount: "1079.10"
                        total_repayment_rate: "82.44"
                        new_user_repayment_rate: "0.00"
                        old_user_repayment_rate: "82.44"
                      pagination:
                        total: 3
                        page: 1
                        pageSize: 10
                        totalPages: 1
                        hasNext: false
                        hasPrev: false
                        data:
                          - channel_name: "qd0805"
                            due_bills_count: 0
                            new_user_bills: 0
                            old_user_bills: 0
                            due_amount: "0.00"
                            new_user_due_amount: "0.00"
                            old_user_due_amount: "0.00"
                            avg_bill_amount: "0.00"
                            repayment_rate: "0.00"
                            new_user_repayment_rate: "0.00"
                            old_user_repayment_rate: "0.00"
                            repayment_users: 0
                            repeat_purchase_users: 0
                            repeat_purchase_rate: ""
                          - channel_name: "qd0806_2"
                            due_bills_count: 38
                            new_user_bills: 0
                            old_user_bills: 38
                            due_amount: "1079.10"
                            new_user_due_amount: "0.00"
                            old_user_due_amount: "1079.10"
                            avg_bill_amount: "28.40"
                            repayment_rate: "82.44"
                            new_user_repayment_rate: "0.00"
                            old_user_repayment_rate: "82.44"
                            repayment_users: 1
                            repeat_purchase_users: 1
                            repeat_purchase_rate: "100.00"
                          - channel_name: "qd0807"
                            due_bills_count: 0
                            new_user_bills: 0
                            old_user_bills: 0
                            due_amount: "0.00"
                            new_user_due_amount: "0.00"
                            old_user_due_amount: "0.00"
                            avg_bill_amount: "0.00"
                            repayment_rate: "0.00"
                            new_user_repayment_rate: "0.00"
                            old_user_repayment_rate: "0.00"
                            repayment_users: 0
                            repeat_purchase_users: 0
                            repeat_purchase_rate: ""
                    exdata: null
                    token: ""
                    time: 1755164752
                no_orders_channels:
                  summary: 只有无订单渠道的响应示例
                  value:
                    code: 0
                    message: "获取渠道到期统计成功"
                    data:
                      statistics:
                        total_due_bills: 0
                        new_user_bills: 0
                        old_user_bills: 0
                        total_due_amount: "0.00"
                        new_user_due_amount: "0.00"
                        old_user_due_amount: "0.00"
                        total_repayment_rate: "0.00"
                        new_user_repayment_rate: "0.00"
                        old_user_repayment_rate: "0.00"
                      pagination:
                        total: 2
                        page: 1
                        pageSize: 10
                        totalPages: 1
                        hasNext: false
                        hasPrev: false
                        data:
                          - channel_name: "新渠道A"
                            due_bills_count: 0
                            new_user_bills: 0
                            old_user_bills: 0
                            due_amount: "0.00"
                            new_user_due_amount: "0.00"
                            old_user_due_amount: "0.00"
                            avg_bill_amount: "0.00"
                            repayment_rate: "0.00"
                            new_user_repayment_rate: "0.00"
                            old_user_repayment_rate: "0.00"
                            repayment_users: 0
                            repeat_purchase_users: 0
                            repeat_purchase_rate: ""
                          - channel_name: "新渠道B"
                            due_bills_count: 0
                            new_user_bills: 0
                            old_user_bills: 0
                            due_amount: "0.00"
                            new_user_due_amount: "0.00"
                            old_user_due_amount: "0.00"
                            avg_bill_amount: "0.00"
                            repayment_rate: "0.00"
                            new_user_repayment_rate: "0.00"
                            old_user_repayment_rate: "0.00"
                            repayment_users: 0
                            repeat_purchase_users: 0
                            repeat_purchase_rate: ""
                    exdata: null
                    token: ""
                    time: 1755164752
                filtered:
                  summary: 筛选查询响应示例
                  value:
                    code: 0
                    message: "获取渠道到期统计成功"
                    data:
                      statistics:
                        total_due_bills: 15
                        new_user_bills: 5
                        old_user_bills: 10
                        total_due_amount: "500.00"
                        new_user_due_amount: "150.00"
                        old_user_due_amount: "350.00"
                        total_repayment_rate: "85.00"
                        new_user_repayment_rate: "80.00"
                        old_user_repayment_rate: "87.14"
                      pagination:
                        total: 1
                        page: 1
                        pageSize: 10
                        totalPages: 1
                        hasNext: false
                        hasPrev: false
                        data:
                          - channel_name: "渠道A"
                            due_bills_count: 15
                            new_user_bills: 5
                            old_user_bills: 10
                            due_amount: "500.00"
                            new_user_due_amount: "150.00"
                            old_user_due_amount: "350.00"
                            avg_bill_amount: "33.33"
                            repayment_rate: "85.00"
                            new_user_repayment_rate: "80.00"
                            old_user_repayment_rate: "87.14"
                            repayment_users: 12
                            repeat_purchase_users: 8
                            repeat_purchase_rate: "66.67"
                    exdata: null
                    token: ""
                    time: **********
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取渠道到期统计成功"
                    data:
                      statistics:
                        total_due_bills: 0
                        new_user_bills: 0
                        old_user_bills: 0
                        total_due_amount: "0.00"
                        new_user_due_amount: "0.00"
                        old_user_due_amount: "0.00"
                        total_repayment_rate: "0.00"
                        new_user_repayment_rate: "0.00"
                        old_user_repayment_rate: "0.00"
                      pagination:
                        total: 0
                        page: 1
                        pageSize: 10
                        totalPages: 0
                        hasNext: false
                        hasPrev: false
                        data: []
                    exdata: null
                    token: ""
                    time: **********
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date_format:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: **********
                invalid_enum:
                  summary: 枚举值错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "是否新用户值只能为 0 或 1 的整数"
                    exdata: null
                    token: ""
                    time: **********
                invalid_page:
                  summary: 分页参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: **********
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取渠道到期统计失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: **********
                query_error:
                  summary: 查询错误
                  value:
                    code: 1
                    message: "获取渠道到期统计失败"
                    data: "获取基础账单统计失败"
                    exdata: null
                    token: ""
                    time: **********

  # 支出明细接口
  /business/statistics/statisticscontroller/getExpenseDetails:
    get:
      tags:
        - 统计管理
      summary: 获取支出明细
      description: |
        获取支出明细统计和列表数据，支持多种筛选条件和分页查询。

        **业务说明：**
        - 以订单表为主表，关联用户表、合同表、银行卡表获取完整信息
        - 只统计已放款的订单（状态为1-放款中或3-交易完成）
        - 统计数据和列表数据使用相同的筛选条件，确保数据一致性
        - 支持多维度筛选：时间范围、渠道来源、用户姓名、手机号
        - 使用并行查询优化性能

        **新老用户判断逻辑：**
        - 新用户：放款订单数量等于1的用户
        - 旧用户：放款订单数量大于1的用户

        **支出账户获取逻辑：**
        - 通过订单→合同→银行卡的关联路径获取银行卡号
        - 支付方式固定为"统统付小贷打款"

        **数据关联关系：**
        ```
        business_loan_orders (订单表，主表)
        ├── business_app_account (用户表) - 通过 user_id
        ├── contracts (合同表) - 通过 contract_id
        └── business_bank_cards (银行卡表) - 通过 contracts.bank_card_id
        ```

        **统计数据说明：**
        - 支出总额：所有放款订单的金额总和
        - 新用户支出总额：新用户放款订单的金额总和
        - 旧用户支出总额：旧用户放款订单的金额总和
        - 今日支出：当日放款订单的金额总和
        - 支出笔数：放款订单的总数量
        - 支出订单数：同支出笔数
        - 支出人数：有放款记录的唯一用户数

        **权限要求：**
        - 需要登录用户权限
        - 需要支出明细查看权限
      parameters:
        - name: date_start
          in: query
          description: 开始日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-01-01"
        - name: date_end
          in: query
          description: 结束日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-01-31"
        - name: channel_id
          in: query
          description: 渠道ID（可选）
          required: false
          schema:
            type: integer
            example: 1
        - name: user_name
          in: query
          description: 用户姓名（模糊匹配，可选）
          required: false
          schema:
            type: string
            example: "张三"
        - name: mobile
          in: query
          description: 手机号（模糊匹配，可选）
          required: false
          schema:
            type: string
            pattern: '^1[3-9]\\d{9}$'
            example: "***********"
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
      responses:
        '200':
          description: 获取支出明细成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExpenseDetailsResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    message: "获取支出明细成功"
                    data:
                      statistics:
                        total_expense: 1000000.50
                        new_user_expense: 300000.25
                        old_user_expense: 700000.25
                        today_expense: 50000.00
                        expense_count: 150
                        expense_orders: 150
                        expense_users: 120
                      list:
                        total: 150
                        page: 1
                        pageSize: 10
                        totalPages: 15
                        hasNext: true
                        hasPrev: false
                        data:
                          - order_no: "ORD20240101001"
                            amount: "5000.00"
                            user_name: "张三"
                            mobile: "***********"
                            bank_card_no: "6222021234567890123"
                            payment_method: "统统付小贷打款"
                            disbursed_at: "2024-01-01 10:30:00"
                          - order_no: "ORD20240101002"
                            amount: "8000.00"
                            user_name: "李四"
                            mobile: "***********"
                            bank_card_no: "6222021234567890456"
                            payment_method: "统统付小贷打款"
                            disbursed_at: "2024-01-01 14:20:00"
                    exdata: null
                    token: ""
                    time: **********
                filtered:
                  summary: 筛选查询响应示例
                  value:
                    code: 0
                    message: "获取支出明细成功"
                    data:
                      statistics:
                        total_expense: 50000.00
                        new_user_expense: 20000.00
                        old_user_expense: 30000.00
                        today_expense: 5000.00
                        expense_count: 10
                        expense_orders: 10
                        expense_users: 8
                      list:
                        total: 10
                        page: 1
                        pageSize: 10
                        totalPages: 1
                        hasNext: false
                        hasPrev: false
                        data:
                          - order_no: "ORD20240101001"
                            amount: "5000.00"
                            user_name: "张三"
                            mobile: "***********"
                            bank_card_no: "6222021234567890123"
                            payment_method: "统统付小贷打款"
                            disbursed_at: "2024-01-01 10:30:00"
                    exdata: null
                    token: ""
                    time: **********
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取支出明细成功"
                    data:
                      statistics:
                        total_expense: 0.00
                        new_user_expense: 0.00
                        old_user_expense: 0.00
                        today_expense: 0.00
                        expense_count: 0
                        expense_orders: 0
                        expense_users: 0
                      list:
                        total: 0
                        page: 1
                        pageSize: 10
                        totalPages: 0
                        hasNext: false
                        hasPrev: false
                        data: []
                    exdata: null
                    token: ""
                    time: **********
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date_format:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: **********
                invalid_mobile:
                  summary: 手机号格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "手机号格式错误"
                    exdata: null
                    token: ""
                    time: **********
                invalid_page:
                  summary: 分页参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: **********
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取支出明细失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: **********
                query_error:
                  summary: 查询错误
                  value:
                    code: 1
                    message: "获取支出明细失败"
                    data: "查询支出明细列表失败"
                    exdata: null
                    token: ""
                    time: **********

tags:
  - name: 统计管理
    description: 统计管理模块相关接口，包括首页统计、趋势统计、渠道统计、收入明细、支出明细、渠道到期统计等功能
