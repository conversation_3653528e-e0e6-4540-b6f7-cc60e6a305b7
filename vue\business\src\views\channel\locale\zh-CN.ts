export default {
  // 菜单相关
  'menu.channel': '渠道管理',
  'menu.channel.list': '渠道列表',
  
  // 通用操作
  'channel.operation.create': '新增渠道',
  'channel.operation.edit': '编辑渠道',
  'channel.operation.view': '查看渠道',
  'channel.operation.delete': '删除渠道',
  'channel.operation.loanRules': '放款规则',
  
  // 表格列头
  'channel.columns.id': 'ID',
  'channel.columns.channelName': '渠道名称',
  'channel.columns.channelCode': '渠道编码',
  'channel.columns.domain': '域名',
  'channel.columns.mobile': '手机号',
  'channel.columns.channelStatus': '渠道状态',
  'channel.columns.channelUsage': '使用状态',
  'channel.columns.createTime': '创建时间',
  'channel.columns.operations': '操作',
  
  // 状态
  'channel.status.enabled': '启用',
  'channel.status.disabled': '禁用',
  'channel.usage.normal': '正常',
  'channel.usage.maintenance': '维护中',
  
  // 表单
  'channel.form.channelName': '渠道名称',
  'channel.form.channelCode': '渠道编码',
  'channel.form.domain': '域名',
  'channel.form.mobile': '手机号',
  'channel.form.channelStatus': '渠道状态',
  'channel.form.channelUsage': '使用状态',
  'channel.form.channelName.placeholder': '请输入渠道名称',
  'channel.form.channelCode.placeholder': '请输入渠道编码',
  'channel.form.domain.placeholder': '请输入域名',
  'channel.form.mobile.placeholder': '请输入手机号',
  
  // 验证消息
  'channel.form.channelName.required': '请输入渠道名称',
  'channel.form.channelCode.required': '请输入渠道编码',
  'channel.form.domain.required': '请输入域名',
  'channel.form.mobile.required': '请输入手机号',
  'channel.form.mobile.pattern': '请输入正确的手机号格式',
  
  // 消息提示
  'channel.message.createSuccess': '创建渠道成功',
  'channel.message.updateSuccess': '更新渠道成功',
  'channel.message.deleteSuccess': '删除渠道成功',
  'channel.message.deleteConfirm': '确定要删除该渠道吗？',
  'channel.message.generateCodeSuccess': '生成渠道编码成功',
  
  // 搜索表单
  'channel.search.channelName': '渠道名称',
  'channel.search.channelCode': '渠道编码',
  'channel.search.channelStatus': '渠道状态',
  'channel.search.placeholder.channelName': '请输入渠道名称',
  'channel.search.placeholder.channelCode': '请输入渠道编码',
  'channel.search.placeholder.all': '全部',
  
  // 放款规则
  'channel.loanRules.title': '放款规则管理',
  'channel.loanRules.ruleId': '规则ID',
  'channel.loanRules.minRiskScore': '最小风控分',
  'channel.loanRules.maxRiskScore': '最大风控分',
  'channel.loanRules.addRule': '添加规则',
  'channel.loanRules.saveSuccess': '保存规则成功',

  // 渠道统计
  'menu.channel.statistics': '渠道统计',
};