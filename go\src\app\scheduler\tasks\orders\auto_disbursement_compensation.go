package orders

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/order"
	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"

	"github.com/gogf/gf/v2/util/gconv"
)

// AutoDisbursementCompensationTask 自动放款补偿任务
// 用于处理创建订单后自动放款异常或失败的补偿场景
type AutoDisbursementCompensationTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewAutoDisbursementCompensationTask 创建自动放款补偿任务
func NewAutoDisbursementCompensationTask() *AutoDisbursementCompensationTask {
	baseTask := tasks.NewBaseTask(
		"auto-disbursement-compensation",
		"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿",
		"0 */3 * * * *", // 每 3 分钟执行一次
		5*time.Minute,   // 超时时间5分钟
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("order_task", "订单任务")
	ctx := context.Background()
	return &AutoDisbursementCompensationTask{
		BaseTask: baseTask,
		logger:   logger,
		ctx:      ctx,
	}
}

// Execute 执行自动放款补偿任务
func (t *AutoDisbursementCompensationTask) Execute(ctx context.Context) error {

	orderService := order.NewOrderServiceWithOptions(
		t.ctx,
		order.WithLogger(t.logger),
		order.WithBankCardModel(),
		order.WithOrderModel(),
		order.WithContractModel(),
		order.WithTransactionModel(),
	)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行自动放款补偿任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	// 查询符合条件的订单
	orders, err := t.getPendingAutoDisbursementOrders()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_orders"),
			log.String("error", err.Error()),
		).Error("查询待自动放款订单失败")
		return fmt.Errorf("查询待自动放款订单失败: %v", err)
	}

	if len(orders) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_orders_found"),
		).Info("未找到需要补偿放款的订单")
		return nil
	}

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "found_orders"),
		log.Int("order_count", len(orders)),
	).Info("找到需要补偿放款的订单")

	// 遍历处理每个订单
	for _, orderData := range orders {
		processedCount++

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "context_cancelled"),
				log.Int("processed_count", processedCount-1),
			).Warn("任务被取消，停止处理")
			return ctx.Err()
		default:
		}

		orderNo := convert.GetStringFromMap(orderData, "order_no")
		orderID := convert.GetIntFromMap(orderData, "id", 0)
		userID := convert.GetIntFromMap(orderData, "user_id", 0)

		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("order_no", orderNo),
			log.Int("order_id", orderID),
			log.String("operation", "process_disbursement"),
		).Info("开始处理订单自动放款")

		// 调用现有的放款方法
		_, err := orderService.ExecuteDisbursementForCompensation(orderNo, userID)
		if err != nil {
			failureCount++
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("order_no", orderNo),
				log.Int("order_id", orderID),
				log.String("operation", "disbursement_failed"),
				log.String("error", err.Error()),
			).Error("订单自动放款失败")
			continue
		}

		successCount++
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("order_no", orderNo),
			log.Int("order_id", orderID),
			log.String("operation", "disbursement_success"),
		).Info("订单自动放款成功")
	}

	// 记录任务执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("total_orders", len(orders)),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("自动放款补偿任务执行完成")

	return nil
}

const (
	ErrorCodeAccountBalanceInsufficient = "400046" // 放款账户余额不足
)

// getPendingAutoDisbursementOrders 获取待自动放款的订单
func (t *AutoDisbursementCompensationTask) getPendingAutoDisbursementOrders() ([]gform.Data, error) {

	// 查询最新一笔放款流水
	res, err := model.DB().Table("business_payment_transactions").
		Where("type", "DISBURSEMENT").
		OrderBy("id DESC").First()

	if err != nil {
		return nil, fmt.Errorf("查询放款流水失败: %v", err)
	}

	if res != nil {
		transaction := model.BusinessPaymentTransactions{}

		err = gconv.Struct(res, &transaction)

		if err != nil {
			return nil, fmt.Errorf("转换放款流水失败: %v", err)
		}

		// 如果最新一笔放款流水的错误码不为空则暂停补偿
		if transaction.ErrorCode != "" {
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "no_compensation"),
				log.String("error_code", transaction.ErrorCode),
			).Warn("最新一笔放款流水错误码为放款账户余额不足，不进行补偿")
			return nil, nil
		}
	}

	// 查询条件：
	// 1. 订单状态为待放款 (status = 0)
	// 2. 渠道配置为自动放款 (auto_label = 0)
	// 3. 渠道状态为启用 (channel_status = 1)
	// 4. 风控结果为通过 (risk_control_results = 0)
	query := `
		SELECT 
			blo.id,
			blo.order_no,
			blo.user_id,
			blo.channel_id,
			blo.loan_amount,
			blo.created_at,
			c.channel_name,
			c.auto_label
		FROM business_loan_orders blo
		LEFT JOIN channel c ON blo.channel_id = c.id
		WHERE blo.status = ?
		  AND blo.risk_control_results = 0
		  AND c.auto_label = 0
		  AND c.channel_status = 1
		ORDER BY blo.created_at ASC
		LIMIT 100
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query, model.OrderStatusPendingDisbursement)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}

	return result, nil
}

// OnStart 任务开始执行前的回调
func (t *AutoDisbursementCompensationTask) OnStart(ctx context.Context) error {
	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	// 使用自定义类型的常量而不是原始字符串
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("自动放款补偿任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *AutoDisbursementCompensationTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("自动放款补偿任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *AutoDisbursementCompensationTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("自动放款补偿任务执行失败")
	return nil
}

// OnComplete 任务执行完成后的回调
func (t *AutoDisbursementCompensationTask) OnComplete(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_complete"),
	).Info("自动放款补偿任务执行完成")
	return nil
}
