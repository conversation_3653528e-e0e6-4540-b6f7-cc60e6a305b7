package model

import (
	"context"
	"fmt"
	"strconv"
	"time"
)

// BusinessBankCards 银行卡数据模型
type BusinessBankCards struct {
	ID                    int       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`             // 主键ID，自增
	UserID                int       `json:"user_id" db:"user_id"`                                   // 用户ID
	ThirdPartyOrderNo     string    `json:"third_party_order_no" db:"third_party_order_no"`         // 第三方订单号
	BankCardNo            string    `json:"bank_card_no" db:"bank_card_no"`                         // 银行卡号
	BankPhone             string    `json:"bank_phone" db:"bank_phone"`                             // 银行预留手机号
	CardStatus            int       `json:"card_status" db:"card_status"`                           // 卡片状态：0-待验证，1-已绑定，2-绑定失败，3-未知状态
	RespCode              string    `json:"resp_code" db:"resp_code"`                               // 响应码
	RespMsg               string    `json:"resp_msg" db:"resp_msg"`                                 // 响应消息
	BankCode              string    `json:"bank_code" db:"bank_code"`                               // 银行代码
	BankName              string    `json:"bank_name" db:"bank_name"`                               // 银行名称
	CardType              string    `json:"card_type" db:"card_type"`                               // 卡片类型
	CardTypeName          string    `json:"card_type_name" db:"card_type_name"`                     // 卡片类型名称
	BindCardId            string    `json:"bind_card_id" db:"bind_card_id"`                         // 绑卡ID
	SystemDeductFailCount int       `json:"system_deduct_fail_count" db:"system_deduct_fail_count"` // 系统代扣失败次数
	CreatedAt             time.Time `json:"created_at" db:"created_at"`                             // 创建时间
	UpdatedAt             time.Time `json:"updated_at" db:"updated_at"`                             // 更新时间
}

// TableName 指定表名
func (BusinessBankCards) TableName() string {
	return "business_bank_cards"
}

// 银行卡状态常量
const (
	CardStatusPending = 0 // 待验证
	CardStatusBound   = 1 // 已绑定
	CardStatusFailed  = 2 // 绑定失败
	CardStatusUnknown = 3 // 未知状态
)

// BusinessBankCardsService 银行卡服务
type BusinessBankCardsService struct {
	ctx context.Context
}

// NewBusinessBankCardsService 创建银行卡服务实例
func NewBusinessBankCardsService(ctx context.Context) *BusinessBankCardsService {
	return &BusinessBankCardsService{
		ctx: ctx,
	}
}

// CreateBankCard 创建银行卡记录
func (s *BusinessBankCardsService) CreateBankCard(card *BusinessBankCards) error {
	now := time.Now()
	card.CreatedAt = now
	card.UpdatedAt = now

	data := map[string]interface{}{
		"user_id":              card.UserID,
		"third_party_order_no": card.ThirdPartyOrderNo,
		"bank_card_no":         card.BankCardNo,
		"bank_phone":           card.BankPhone,
		"card_status":          card.CardStatus,
		"resp_code":            card.RespCode,
		"resp_msg":             card.RespMsg,
		"bank_code":            card.BankCode,
		"bank_name":            card.BankName,
		"card_type":            card.CardType,
		"card_type_name":       card.CardTypeName,
		"bind_card_id":         card.BindCardId,
		"created_at":           now,
		"updated_at":           now,
	}

	insertID, err := DB().Table("business_bank_cards").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("创建银行卡记录失败: %v", err)
	}

	// 设置插入的ID
	card.ID = int(insertID)

	return nil
}

// GetBankCardByID 根据ID获取银行卡记录
func (s *BusinessBankCardsService) GetBankCardByID(id int) (*BusinessBankCards, error) {
	data, err := DB(WithContext(s.ctx)).Table("business_bank_cards").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡记录失败: %v", err)
	}

	var card BusinessBankCards
	if err := mapToBankCard(data, &card); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &card, nil
}

// GetBankCardByUserIDAndOrderNo 根据用户ID和订单号获取银行卡记录
func (s *BusinessBankCardsService) GetBankCardByUserIDAndOrderNo(userID int, orderNo string) (*BusinessBankCards, error) {

	query := DB().Table("business_bank_cards").Where("user_id", userID).OrderBy("id DESC")

	if orderNo != "" {
		query = query.Where("third_party_order_no", orderNo)
	}

	data, err := query.First()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡记录失败: %v", err)
	}

	var card BusinessBankCards
	if err := mapToStruct(data, &card); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &card, nil
}

// GetBankCardByUserIDAndBankCardNo 根据用户ID和银行卡号获取银行卡记录
func (s *BusinessBankCardsService) GetBankCardByUserIDAndBankCardNo(userID int, bankCardNo string) (*BusinessBankCards, error) {
	data, err := DB().Table("business_bank_cards").Where("user_id", userID).Where("bank_card_no", bankCardNo).First()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡记录失败: %v", err)
	}

	// 检查是否找到记录
	if len(data) == 0 {
		return nil, nil // 记录不存在时返回 nil, nil
	}

	var card BusinessBankCards
	if err := mapToStruct(data, &card); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &card, nil
}

// UpdateBankCardStatus 更新银行卡状态
func (s *BusinessBankCardsService) UpdateBankCardStatus(cardID int, status int, respCode, respMsg, bindCardId string) error {
	now := time.Now()
	updateData := map[string]interface{}{
		"card_status":  status,
		"resp_code":    respCode,
		"resp_msg":     respMsg,
		"bind_card_id": bindCardId,
		"updated_at":   now,
	}

	_, err := DB().Table("business_bank_cards").Where("id", cardID).Update(updateData)
	if err != nil {
		return fmt.Errorf("更新银行卡状态失败: %v", err)
	}

	return nil
}

// UpdateBankCardInfo 更新银行卡信息
func (s *BusinessBankCardsService) UpdateBankCardInfo(cardID int, updateData map[string]interface{}) error {
	updateData["updated_at"] = time.Now()

	_, err := DB().Table("business_bank_cards").Where("id", cardID).Update(updateData)
	if err != nil {
		return fmt.Errorf("更新银行卡信息失败: %v", err)
	}

	return nil
}

// IncrementSystemDeductFailCount 增加系统代扣失败次数
func (s *BusinessBankCardsService) IncrementSystemDeductFailCount(bankCardNo string, userID int) error {
	// 使用原生SQL进行字段自增操作
	sql := "UPDATE business_bank_cards SET system_deduct_fail_count = system_deduct_fail_count + 1, updated_at = ? WHERE bank_card_no = ? AND user_id = ?"
	_, err := DBEV().GetExecuteDB().Exec(sql, time.Now(), bankCardNo, userID)
	if err != nil {
		return fmt.Errorf("增加系统代扣失败次数失败: %v", err)
	}
	return nil
}

// GetBankCardsWithFailCountLimit 获取失败次数未超过限制的银行卡
func (s *BusinessBankCardsService) GetBankCardsWithFailCountLimit(userID int, maxFailCount int) ([]BusinessBankCards, error) {
	data, err := DB().Table("business_bank_cards").
		Where("user_id", userID).
		Where("card_status", CardStatusBound). // 只获取已绑定的银行卡
		Where("system_deduct_fail_count", "<", maxFailCount).
		OrderBy("id").
		Get()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡列表失败: %v", err)
	}

	var cards []BusinessBankCards
	for _, item := range data {
		var card BusinessBankCards
		if err := mapToBankCard(item, &card); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		cards = append(cards, card)
	}

	return cards, nil
}

// GetBankCardsByUserID 根据用户ID获取银行卡列表
func (s *BusinessBankCardsService) GetBankCardsByUserID(userID int) ([]BusinessBankCards, error) {
	data, err := DB().Table("business_bank_cards").Where("user_id", userID).Where("card_status", CardStatusBound).OrderBy("id").Get()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡列表失败: %v", err)
	}

	var cards []BusinessBankCards
	for _, item := range data {
		var card BusinessBankCards
		if err := mapToStruct(item, &card); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		cards = append(cards, card)
	}

	return cards, nil
}

// QueryBankCardsParams 查询银行卡参数
type QueryBankCardsParams struct {
	CardID int
}

// GetBankCardsByCondition 根据条件查询银行卡信息
func (s *BusinessBankCardsService) GetBankCardsByCondition(params QueryBankCardsParams) (*BusinessBankCards, error) {
	db := DB(WithContext(s.ctx)).Table("business_bank_cards")
	if params.CardID > 0 {
		db = db.Where("id", params.CardID)
	}

	data, err := db.OrderBy("id").First()
	if err != nil {
		return nil, fmt.Errorf("查询银行卡列表失败: %v", err)
	}
	// 检查是否找到记录
	if len(data) == 0 {
		return nil, nil // 记录不存在时返回 nil, nil
	}

	var card BusinessBankCards
	if err := mapToStruct(data, &card); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}
	return &card, nil
}

// UpdateUserInfo 更新用户信息
func (s *BusinessBankCardsService) UpdateUserInfo(userID int, data map[string]interface{}) error {
	_, err := DB().Table("business_app_account").Where("id", userID).Update(data)
	if err != nil {
		return fmt.Errorf("更新用户信息失败: %v", err)
	}
	return nil
}

// GetUserInfo 获取用户信息
func (s *BusinessBankCardsService) GetUserInfo(userID int) (map[string]interface{}, error) {
	data, err := DB().Table("business_app_account").Where("id", userID).First()
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	userInfo := map[string]interface{}{
		"name":   data["name"],
		"idCard": data["idCard"],
	}

	return userInfo, nil
}

// mapToBankCard 专门用于将数据映射到BusinessBankCards结构体，处理类型转换
func mapToBankCard(data map[string]interface{}, card *BusinessBankCards) error {
	// 辅助函数：安全地转换为int
	parseInt := func(v interface{}) int {
		switch val := v.(type) {
		case int:
			return val
		case int64:
			return int(val)
		case float64:
			return int(val)
		case string:
			if i, err := strconv.Atoi(val); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为time.Time
	parseTime := func(v interface{}) time.Time {
		switch val := v.(type) {
		case time.Time:
			return val
		case string:
			// 尝试解析常见的时间格式，优先处理ISO 8601格式
			formats := []string{
				// ISO 8601格式（数据库常用）
				"2006-01-02T15:04:05-07:00",     // 带时区偏移
				"2006-01-02T15:04:05+08:00",     // 中国时区
				"2006-01-02T15:04:05Z",          // UTC时区
				"2006-01-02T15:04:05.000Z",      // 带毫秒的UTC
				"2006-01-02T15:04:05.000-07:00", // 带毫秒和时区
				"2006-01-02T15:04:05.000+08:00", // 带毫秒的中国时区
				// 标准格式
				"2006-01-02 15:04:05",
				"2006-01-02",
			}
			for _, format := range formats {
				if t, err := time.Parse(format, val); err == nil {
					return t
				}
			}
			// 尝试使用RFC3339格式（Go标准）
			if t, err := time.Parse(time.RFC3339, val); err == nil {
				return t
			}
			// 尝试解析Unix时间戳字符串
			if i, err := strconv.ParseInt(val, 10, 64); err == nil {
				return time.Unix(i, 0)
			}
		case int64:
			return time.Unix(val, 0)
		case int:
			return time.Unix(int64(val), 0)
		case float64:
			return time.Unix(int64(val), 0)
		}
		return time.Time{}
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		card.ID = parseInt(v)
	}
	if v, ok := data["user_id"]; ok {
		card.UserID = parseInt(v)
	}
	if v, ok := data["third_party_order_no"]; ok {
		card.ThirdPartyOrderNo = parseString(v)
	}
	if v, ok := data["bank_card_no"]; ok {
		card.BankCardNo = parseString(v)
	}
	if v, ok := data["bank_phone"]; ok {
		card.BankPhone = parseString(v)
	}
	if v, ok := data["card_status"]; ok {
		card.CardStatus = parseInt(v)
	}
	if v, ok := data["resp_code"]; ok {
		card.RespCode = parseString(v)
	}
	if v, ok := data["resp_msg"]; ok {
		card.RespMsg = parseString(v)
	}
	if v, ok := data["bank_code"]; ok {
		card.BankCode = parseString(v)
	}
	if v, ok := data["bank_name"]; ok {
		card.BankName = parseString(v)
	}
	if v, ok := data["card_type"]; ok {
		card.CardType = parseString(v)
	}
	if v, ok := data["card_type_name"]; ok {
		card.CardTypeName = parseString(v)
	}
	if v, ok := data["bind_card_id"]; ok {
		card.BindCardId = parseString(v)
	}
	if v, ok := data["system_deduct_fail_count"]; ok {
		card.SystemDeductFailCount = parseInt(v)
	}
	if v, ok := data["created_at"]; ok {
		card.CreatedAt = parseTime(v)
	}
	if v, ok := data["updated_at"]; ok {
		card.UpdatedAt = parseTime(v)
	}

	return nil
}
