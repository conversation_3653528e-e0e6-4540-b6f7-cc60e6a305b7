package productrules

import (
	"errors"
	"fincore/utils/repayment"
	"fincore/utils/shopspringutils"
)

// CheckCaluation 检查账单计算结果
func CheckCaluation(rule *ProductRule) error {

	// 计算产品规则结果
	calculationResult, err := repayment.CalculateRepaymentSchedule(rule.ProductRule)
	if err != nil {
		return err
	}

	// 如果有用户自定义的计算
	// 比较待还本金之和、待还利息之和、待还担保费之和是否相等
	// 比较应还总额是否相等
	// 比较实际放款金额是否相等
	if rule.CustomerCalculation != nil {
		customCalculation := rule.CustomerCalculation
		var totalPrincipal, totalInterest, totalGuaranteeFee float64
		for _, period := range calculationResult.Periods {
			totalPrincipal = shopspringutils.AddAmountsWithDecimal(totalPrincipal, period.DuePrincipal)
			totalInterest = shopspringutils.AddAmountsWithDecimal(totalInterest, period.DueInterest)
			totalGuaranteeFee = shopspringutils.AddAmountsWithDecimal(totalGuaranteeFee, period.DueGuaranteeFee)
		}

		if shopspringutils.CompareAmountsWithDecimal(totalPrincipal, customCalculation.TotalPrincipal) != shopspringutils.Equal {
			return errors.New("待还本金与账单本金不相等")
		}
		if shopspringutils.CompareAmountsWithDecimal(totalInterest, customCalculation.TotalInterest) != shopspringutils.Equal {
			return errors.New("待还利息与账单利息不相等")
		}
		if shopspringutils.CompareAmountsWithDecimal(totalGuaranteeFee, customCalculation.TotalGuaranteeFee) != shopspringutils.Equal {
			return errors.New("待还担保费与账单担保费不相等")
		}

		if shopspringutils.CompareAmountsWithDecimal(calculationResult.TotalRepayableAmount, customCalculation.TotalRepayableAmount) != shopspringutils.Equal {
			return errors.New("应还总额与账单应还总额不相等")
		}

		if shopspringutils.CompareAmountsWithDecimal(calculationResult.DisbursementAmount, customCalculation.DisbursementAmount) != shopspringutils.Equal {
			return errors.New("实际放款金额与账单实际放款金额不相等")
		}
	} else {
		*rule.CustomerCalculation = *calculationResult
	}

	return nil
}
