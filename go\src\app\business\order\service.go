package order

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/risk"
	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/shopspringutils"

	"github.com/golang-module/carbon/v2"
)

// Service 订单服务
type Service struct {
	orderModel          *model.BusinessLoanOrdersService
	productRuleModel    *model.ProductRulesService
	repaymentModel      *model.BusinessRepaymentBillsService
	operationModel      *model.BusinessOrderOperationLogsService
	paymentModel        *model.BusinessPaymentChannelsService
	transactionModel    *model.BusinessPaymentTransactionsService
	bankCardModel       *model.BusinessBankCardsService
	remarksModel        *model.BusinessOrderRemarksService // 订单备注服务
	contractModel       *model.ContractService             // 合同服务
	channelModel        *model.Channel                     // 渠道服务
	channelModelService *model.ChannelService              // 渠道服务
	repository          *Repository                        // 数据仓库层
	logger              *log.Logger
	ctx                 context.Context
}

// OrderListItem 订单列表项结构
type OrderListItem struct {
	ID                      int     `json:"id"`                         // 订单ID
	OrderNo                 string  `json:"order_no"`                   // 订单编号
	UserID                  int     `json:"user_id"`                    // 用户ID
	ProductRuleID           int     `json:"product_rule_id"`            // 产品规则ID
	LoanAmount              float64 `json:"loan_amount"`                // 借款金额
	Principal               float64 `json:"principal"`                  // 实际放款本金
	TotalInterest           float64 `json:"total_interest"`             // 总利息
	TotalGuaranteeFee       float64 `json:"total_guarantee_fee"`        // 总担保费
	TotalOtherFees          float64 `json:"total_other_fees"`           // 总其他费用
	TotalRepayableAmount    float64 `json:"total_repayable_amount"`     // 总应还金额
	AmountPaid              float64 `json:"amount_paid"`                // 已还金额
	PaidPeriods             int     `json:"paid_periods"`               // 已还款期数
	Channel                 string  `json:"channel"`                    // 渠道
	RiskScore               int     `json:"risk_score"`                 // 风控分数
	CustomerOrigin          string  `json:"customer_origin"`            // 客户来源
	Status                  int     `json:"status"`                     // 订单状态
	IsFreeze                int     `json:"is_freeze"`                  // 是否冻结
	ComplaintStatus         int     `json:"complaint_status"`           // 投诉状态
	ReviewStatus            int     `json:"review_status"`              // 复审状态
	SalesAssigneeID         int     `json:"sales_assignee_id"`          // 业务员ID
	CollectionAssigneeID    int     `json:"collection_assignee_id"`     // 催收员ID
	CreatedAt               string  `json:"created_at"`                 // 创建时间
	UpdatedAt               string  `json:"updated_at"`                 // 更新时间
	DisbursedAt             string  `json:"disbursed_at"`               // 放款时间
	CompletedAt             string  `json:"completed_at"`               // 结清/关闭时间
	UserName                string  `json:"user_name"`                  // 用户名
	UserMobile              string  `json:"user_mobile"`                // 用户手机号
	UserIDCard              string  `json:"user_id_card"`               // 用户身份证号（脱敏）
	ProductName             string  `json:"product_name"`               // 产品名称
	LoanPeriod              int     `json:"loan_period"`                // 借款天数
	TotalPeriods            int     `json:"total_periods"`              // 借款总期数
	AuditAssigneeName       string  `json:"audit_assignee_name"`        // 审核员名称
	SalesAssigneeName       string  `json:"sales_assignee_name"`        // 业务员名称
	CollectionAssigneeName  string  `json:"collection_assignee_name"`   // 催收员名称
	ChannelName             string  `json:"channel_name"`               // 渠道名称
	InitialOrderChannelName string  `json:"initial_order_channel_name"` // 初始下单渠道名称
	PaymentChannelName      string  `json:"payment_channel_name"`       // 支付渠道名称
	IsRepeatPurchase        bool    `json:"is_repeat_purchase"`         // 是否为复购
	ReasonForClosure        *int    `json:"reason_for_closure"`         // 关单原因代码
	ReasonForClosureText    string  `json:"reason_for_closure_text"`    // 关单原因文本
}

// OrderBillInfoResponse 订单账单信息响应结构
type OrderBillInfoResponse struct {
	TotalRepayableAmount string               `json:"total_repayable_amount"` // 订单应还总金额
	BillList             []BillInfo           `json:"bill_list"`              // 账单列表
	DisbursementRecords  []DisbursementRecord `json:"disbursement_records"`   // 打款记录
}

// BillInfo 账单信息结构
type BillInfo struct {
	ID                int     `json:"id"`                  // 账单ID
	PeriodNumber      int     `json:"period_number"`       // 序号（期数）
	TotalDueAmount    string  `json:"total_due_amount"`    // 当期账单应还总额
	PaidAmount        string  `json:"paid_amount"`         // 当期已付总额
	TotalRefundAmount string  `json:"total_refund_amount"` // 累计退款金额
	TotalWaiveAmount  string  `json:"total_waive_amount"`  // 累计减免金额
	Status            int     `json:"status"`              // 状态
	StatusText        string  `json:"status_text"`         // 状态文案
	PaymentTime       *string `json:"payment_time"`        // 支付时间
	DueDate           string  `json:"due_date"`            // 还款到期时间
}

// DisbursementRecord 放款记录结构
type DisbursementRecord struct {
	ID                   int     `json:"id"`                     // 序号
	Status               int     `json:"status"`                 // 状态
	StatusText           string  `json:"status_text"`            // 状态文案
	PayoutAccount        string  `json:"payout_account"`         // 支出账户（占位）
	PaymentType          string  `json:"payment_type"`           // 支付类型
	InitiatedAt          string  `json:"initiated_at"`           // 发起时间
	CompletedAt          *string `json:"completed_at"`           // 完成时间
	ChannelTransactionNo *string `json:"channel_transaction_no"` // 渠道流水号
	Amount               string  `json:"amount"`                 // 支付金额
	ErrorMessage         string  `json:"error_message"`          // 错误信息
}

// PaymentRecord 支付记录结构
type PaymentRecord struct {
	ID                   int     `json:"id"`                     // 序号
	TransactionNo        string  `json:"transaction_no"`         // 流水号
	BillID               int     `json:"bill_id"`                // 账单ID
	RefundAmount         string  `json:"refund_amount"`          // 退款金额
	Amount               string  `json:"amount"`                 // 支付金额
	Status               int     `json:"status"`                 // 状态
	StatusText           string  `json:"status_text"`            // 状态文案
	WithholdType         string  `json:"withhold_type"`          // 代扣类型
	WithholdTypeText     string  `json:"withhold_type_text"`     // 代扣类型文案
	Type                 string  `json:"type"`                   // 交易类型
	Image                string  `json:"image"`                  // 图片（预留）
	PaymentType          string  `json:"payment_type"`           // 支付类型
	InitiatedAt          string  `json:"initiated_at"`           // 支付发起时间
	CompletedAt          *string `json:"completed_at"`           // 支付完成时间
	PeriodNumber         *int    `json:"period_number"`          // 支付期数
	ChannelTransactionNo string  `json:"channel_transaction_no"` // 支付渠道流水号
	ErrorMessage         string  `json:"error_message"`          // 订单响应结果
}

func WithOrderModel() func(*Service) {
	return func(service *Service) {
		service.orderModel = model.NewBusinessLoanOrdersService(service.ctx)
	}
}

func WithProductRuleModel() func(*Service) {
	return func(service *Service) {
		service.productRuleModel = model.NewProductRulesService()
	}
}

func WithBankCardModel() func(*Service) {
	return func(service *Service) {
		service.bankCardModel = model.NewBusinessBankCardsService(service.ctx)
	}
}

func WithRepaymentModel() func(*Service) {
	return func(service *Service) {
		service.repaymentModel = model.NewBusinessRepaymentBillsService(service.ctx)
	}
}

func WithOperationModel() func(*Service) {
	return func(service *Service) {
		service.operationModel = model.NewBusinessOrderOperationLogsService()
	}
}

func WithPaymentModel() func(*Service) {
	return func(service *Service) {
		service.paymentModel = model.NewBusinessPaymentChannelsService()
	}
}

func WithTransactionModel() func(*Service) {
	return func(service *Service) {
		service.transactionModel = model.NewBusinessPaymentTransactionsService(service.ctx)
	}
}

func WithRemarksModel() func(*Service) {
	return func(service *Service) {
		service.remarksModel = &model.BusinessOrderRemarksService{}
	}
}

func WithContractModel() func(*Service) {
	return func(service *Service) {
		service.contractModel = model.NewContractService(service.ctx)
	}
}

func WithChannelService() func(*Service) {
	return func(service *Service) {
		service.channelModel = model.NewChannel()
	}
}

func WithChannelModelService() func(*Service) {
	return func(service *Service) {
		service.channelModelService = model.NewChannelService()
	}
}

func WithRepository() func(*Service) {
	return func(service *Service) {
		service.repository = NewRepository(service.ctx)
	}
}

func WithLogger(logger *log.Logger) func(*Service) {
	return func(service *Service) {
		service.logger = logger
	}
}

// NewOrderServiceWithOptions 创建订单服务实例，支持选项模式
// todo 后续性能优化点，使用此函数实例化，可按需注入实例化
func NewOrderServiceWithOptions(ctx context.Context, options ...func(*Service)) *Service {
	service := &Service{
		ctx:    ctx,
		logger: log.Order().WithContext(ctx),
	}
	for _, option := range options {
		option(service)
	}
	return service
}

// NewOrderService 创建订单服务实例
func NewOrderService(ctx context.Context) *Service {
	return &Service{
		orderModel:          model.NewBusinessLoanOrdersService(ctx),
		productRuleModel:    model.NewProductRulesService(),
		bankCardModel:       model.NewBusinessBankCardsService(ctx),
		repaymentModel:      model.NewBusinessRepaymentBillsService(ctx),
		operationModel:      model.NewBusinessOrderOperationLogsService(),
		paymentModel:        model.NewBusinessPaymentChannelsService(),
		transactionModel:    model.NewBusinessPaymentTransactionsService(ctx),
		remarksModel:        model.NewBusinessOrderRemarksService(),
		contractModel:       model.NewContractService(ctx),
		channelModel:        model.NewChannel(),
		channelModelService: model.NewChannelService(),
		repository:          NewRepository(ctx),
		logger:              log.Order().WithContext(ctx),
		ctx:                 ctx,
	}
}

// WaiveBillAmount 减免账单金额
func (s *Service) WaiveBillAmount(billID int, waiveAmount float64, operatorID int, operatorName string) error {
	// 1. 参数验证
	if billID <= 0 {
		return fmt.Errorf("账单ID无效")
	}
	if waiveAmount <= 0 {
		return fmt.Errorf("减免金额必须大于0")
	}

	// 2. 账单维度加锁，防止并发操作
	lockKey := fmt.Sprintf("repayment:bill:%d", billID)
	billLock := lock.GetLock(lockKey, 5*time.Minute)
	billLock.Lock()
	defer billLock.Unlock()

	// 3.1 查询当前账单信息
	bill, err := s.repository.GetBillByIDInTransaction(model.DB(), billID)
	if err != nil {
		return fmt.Errorf("查询账单失败: %v", err)
	}
	if bill == nil {
		return fmt.Errorf("账单不存在")
	}

	// 3. 使用数据库事务保证数据原子性
	err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {

		// 验证减免金额不能超过当期应还总额 - 已还金额 - 已减免金额，保留两位小数避免精度问题
		currentTotalDue := shopspringutils.SubtractAmountsWithDecimal(float64(bill.TotalDueAmount), float64(bill.PaidAmount))
		currentTotalDue = shopspringutils.SubtractAmountsWithDecimal(currentTotalDue, float64(bill.TotalWaiveAmount))

		if waiveAmount > currentTotalDue {
			return fmt.Errorf("最多还能减免金额为%.2f", currentTotalDue)
		}

		now := time.Now()
		newWaiveAmount := shopspringutils.AddAmountsWithDecimal(float64(bill.TotalWaiveAmount), waiveAmount)
		updateBillMaps := map[string]interface{}{
			"total_waive_amount": newWaiveAmount,
			"updated_at":         &now,
		}

		// 账单完结
		var billEnd bool
		if waiveAmount == currentTotalDue {
			billEnd = true
			updateBillMaps["paid_at"] = &now
			dueTime := carbon.CreateFromStdTime(bill.DueDate).EndOfDay().StdTime()
			if time.Now().After(dueTime) {
				// 逾期已支付
				updateBillMaps["status"] = model.RepaymentBillStatusOverduePaid
			} else {
				// 已支付
				updateBillMaps["status"] = model.RepaymentBillStatusPaid
			}
		}

		// 更新账单信息
		err = s.repository.UpdateBillAmounts(tx, billID, updateBillMaps)

		if err != nil {
			return fmt.Errorf("更新账单金额失败: %v", err)
		}

		if billEnd {
			// 查询订单
			order, err := s.orderModel.GetOrderByID(tx, bill.OrderID)
			if err != nil {
				return fmt.Errorf("查询订单失败: %v", err)
			}
			if order == nil {
				return fmt.Errorf("订单不存在")
			}

			// 更新用户剩余额度, 撤回占用额度
			err = s.repository.UpdateUserRemainingAmount(tx, order, float64(bill.DuePrincipal), true)
			if err != nil {
				return fmt.Errorf("更新用户剩余额度失败: %v", err)
			}
		}

		// 判断订单完结
		// 查询所有关联账单状态，如果都为已支付，则将订单状态改为已结清
		billsService := model.NewBusinessRepaymentBillsService(s.ctx)
		bills, err := billsService.GetBillsByOrderID(tx, bill.OrderID)
		if err != nil {
			return fmt.Errorf("查询账单失败: %v", err)
		}
		allPaid := true
		for _, bill := range bills {
			if bill.Status != model.RepaymentBillStatusPaid &&
				bill.Status != model.RepaymentBillStatusOverduePaid &&
				bill.Status != model.RepaymentBillStatusEarlySettlement &&
				bill.Status != model.RepaymentBillStatusSettled {
				allPaid = false
				break
			}
		}
		if allPaid {
			// 将订单状态改为已结清
			err = s.orderModel.UpdateOrderStatus(tx, bill.OrderID, model.OrderStatusCompleted)
			if err != nil {
				return fmt.Errorf("更新订单状态失败: %v", err)
			}
		}

		// 3.7 记录操作日志
		logDetails := fmt.Sprintf("减免账单金额: 账单ID=%d, 减免金额=%.2f, 原应还总额=%.2f",
			billID, waiveAmount, currentTotalDue)

		err = s.repository.CreateOperationLogInTransaction(tx, bill.OrderID, operatorID, operatorName, "减免账单金额", logDetails)
		if err != nil {
			// 日志创建失败不影响主流程，只记录错误
			s.logger.WithFields(
				log.Int("bill_id", billID),
				log.Int("order_id", bill.OrderID),
				log.String("operation", "waive_bill_amount"),
				log.ErrorField(err),
			).Error("创建操作日志失败")
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 刷新风控数据
	return s.RefreshRiskData(bill.UserID)
}

// OrderProgressResponse 订单进度响应结构
type OrderProgressResponse struct {
	OrderCreatedAt     string              `json:"order_created_at"`     // 订单创建时间
	CustomerName       string              `json:"customer_name"`        // 订单所属客户名
	RiskPassedAt       *string             `json:"risk_passed_at"`       // 风控通过时间
	FinalStatus        string              `json:"final_status"`         // 最终订单状态：closed/disbursed
	OrderClosedInfo    *OrderClosedInfo    `json:"order_closed_info"`    // 订单关闭信息
	OrderDisbursedInfo *OrderDisbursedInfo `json:"order_disbursed_info"` // 订单打款信息
}

// OrderClosedInfo 订单关闭信息
type OrderClosedInfo struct {
	ClosedAt          string `json:"closed_at"`           // 订单关闭时间
	ClosureRemarks    string `json:"closure_remarks"`     // 订单关闭备注
	AuditAssigneeName string `json:"audit_assignee_name"` // 审核员名称
}

// OrderDisbursedInfo 订单打款信息
type OrderDisbursedInfo struct {
	DisbursementID string `json:"disbursement_id"` // 打款编号
	OperatorName   string `json:"operator_name"`   // 打款操作人名
	DisbursedAt    string `json:"disbursed_at"`    // 打款时间
}

// GetOrderProgress 获取订单进度
func (s *Service) GetOrderProgress(orderNo string) (*OrderProgressResponse, error) {
	if orderNo == "" {
		return nil, fmt.Errorf("订单编号不能为空")
	}

	// 1. 根据订单号获取订单基本信息
	order, err := s.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 2. 获取客户姓名
	customerName, err := s.repository.GetCustomerNameByUserID(int(order.UserID))
	if err != nil {
		return nil, fmt.Errorf("获取客户姓名失败: %v", err)
	}

	// 3. 获取风控通过时间
	riskPassedAt, err := s.repository.GetRiskPassedTimeByUserID(int(order.UserID))
	if err != nil {
		s.logger.WithFields(
			log.String("order_no", orderNo),
			log.Int("user_id", int(order.UserID)),
			log.ErrorField(err),
		).Warn("获取风控通过时间失败")
		// 风控时间获取失败不影响主流程，继续执行
	}

	// 4. 构建响应数据
	response := &OrderProgressResponse{
		OrderCreatedAt: order.CreatedAt.Format("2006-01-02 15:04:05"),
		CustomerName:   customerName,
		RiskPassedAt:   riskPassedAt,
	}

	// 5. 根据订单状态设置最终状态和相关信息
	switch order.Status {
	case 2: // 交易关闭
		response.FinalStatus = "closed"
		closedInfo, err := s.getOrderClosedInfo(order)
		if err != nil {
			return nil, fmt.Errorf("获取订单关闭信息失败: %v", err)
		}
		response.OrderClosedInfo = closedInfo

	case 1, 3: // 放款中或交易完成
		response.FinalStatus = "disbursed"
		disbursedInfo, err := s.getOrderDisbursedInfo(order)
		if err != nil {
			return nil, fmt.Errorf("获取订单打款信息失败: %v", err)
		}
		response.OrderDisbursedInfo = disbursedInfo

	default:

	}

	return response, nil
}

// getOrderClosedInfo 获取订单关闭信息
func (s *Service) getOrderClosedInfo(order *model.BusinessLoanOrders) (*OrderClosedInfo, error) {
	closedInfo := &OrderClosedInfo{
		ClosureRemarks:    "",
		AuditAssigneeName: "",
	}

	// 设置关闭时间
	if order.CompletedAt != nil {
		closedInfo.ClosedAt = order.CompletedAt.Format("2006-01-02 15:04:05")
	} else {
		return nil, fmt.Errorf("订单关闭时间为空")
	}

	// 设置关闭备注
	if order.ClosureRemarks != nil {
		closedInfo.ClosureRemarks = *order.ClosureRemarks
	}

	// 风控关闭
	if order.ReasonForClosure != nil && *order.ReasonForClosure == -1 {
		closedInfo.AuditAssigneeName = "系统自动"
		return closedInfo, nil
	}

	// 获取审核员名称
	if order.SalesAssigneeID != nil && *order.SalesAssigneeID > 0 {
		auditAssigneeName, err := s.repository.GetSalesAssigneeNameByID(int(*order.SalesAssigneeID))
		if err != nil {
			s.logger.WithFields(
				log.Int("sales_assignee_id", int(*order.SalesAssigneeID)),
				log.ErrorField(err),
			).Warn("获取审核员名称失败")
			// 审核员名称获取失败不影响主流程，使用默认值
			closedInfo.AuditAssigneeName = "未知审核员"
		} else {
			closedInfo.AuditAssigneeName = auditAssigneeName
		}
	} else {
		closedInfo.AuditAssigneeName = "系统自动"
	}

	return closedInfo, nil
}

// getOrderDisbursedInfo 获取订单打款信息
func (s *Service) getOrderDisbursedInfo(order *model.BusinessLoanOrders) (*OrderDisbursedInfo, error) {
	disbursedInfo := &OrderDisbursedInfo{
		DisbursementID: fmt.Sprintf("DIS%s", order.OrderNo), // 生成打款编号
		OperatorName:   "",
	}

	// 设置打款时间
	if order.DisbursedAt != nil {
		disbursedInfo.DisbursedAt = order.DisbursedAt.Format("2006-01-02 15:04:05")
	} else {
		return nil, fmt.Errorf("订单打款时间为空")
	}

	// 获取操作人名称
	if order.SalesAssigneeID != nil && *order.SalesAssigneeID > 0 {
		operatorName, err := s.repository.GetSalesAssigneeNameByID(int(*order.SalesAssigneeID))
		if err != nil {
			s.logger.WithFields(
				log.Int("sales_assignee_id", int(*order.SalesAssigneeID)),
				log.ErrorField(err),
			).Warn("获取操作人名称失败")
			// 操作人名称获取失败不影响主流程，使用默认值
			disbursedInfo.OperatorName = "未知操作员"
		} else {
			disbursedInfo.OperatorName = operatorName
		}
	} else {
		// sales_assignee_id 为0时显示风控自动放款
		disbursedInfo.OperatorName = "风控自动放款"
	}

	return disbursedInfo, nil
}

// RefreshRiskData 订单完结刷新风控数据
func (s *Service) RefreshRiskData(userID int) (err error) {
	//判断当前用户是否存在在途订单
	count, err := s.orderModel.CountInProgressOrdersByUserID(model.DB(model.WithContext(s.ctx)), userID)
	if err != nil {
		s.logger.WithFields(
			log.String("operation", "refresh_risk_data"),
			log.ErrorField(err),
		).Error("风控刷新-查询在途订单失败")
		return
	}

	// 没有在途订单，则刷新风控数据, 刷新失败不影响流程
	if count == 0 {
		accountService := model.NewBusinessAppAccountService()
		userInfo, err := accountService.GetAccountByID(int64(userID))
		if err != nil {
			s.logger.WithFields(
				log.String("operation", "refresh_risk_data"),
				log.ErrorField(err),
			).Error("风控刷新-查询用户信息失败")
		}

		_, err = risk.NewRiskService(s.ctx).PerformNewEvaluation(s.ctx, model.DB(model.WithContext(s.ctx)), userInfo)
		if err != nil {
			s.logger.WithFields(
				log.String("operation", "refresh_risk_data"),
				log.ErrorField(err),
			).Error("刷新风控数据失败")
		}
	}

	return
}
