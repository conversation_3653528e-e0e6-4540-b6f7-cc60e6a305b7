package order

import "fincore/utils/jsonschema"

// floatPtr 返回float64指针，用于jsonschema的Min/Max字段
func floatPtr(f float64) *float64 {
	return &f
}

// GetOrderProgressSchema 订单进度查询参数验证规则
func GetOrderProgressSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单进度查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   32,
				Description: "订单编号（必填，1-32个字符）",
			},
		},
		Required: []string{"order_no"},
	}
}

// GetWaiveBillAmountSchema 减免账单金额参数验证规则
func GetWaiveBillAmountSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "减免账单金额参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "integer",
				Required:    true,
				Min:         floatPtr(1),
				Description: "账单ID（必填，正整数）",
			},
			"waive_amount": {
				Type:        "number",
				Required:    true,
				Min:         floatPtr(0.01),
				Max:         floatPtr(*********.99),
				Description: "减免金额（必填，大于0.01，小于等于*********.99）",
			},
		},
		Required: []string{"bill_id", "waive_amount"},
	}
}

// GetDisburseOrderSchema 订单放款参数验证规则
func GetDisburseOrderSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单放款参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderNo": {
				Type:        "string",
				Required:    true,
				Description: "订单编号",
			},
			"bankCardNo": {
				Type:        "string",
				Required:    true,
				Pattern:     "^\\d{16,19}$",
				Description: "银行卡号",
			},
			"bankName": {
				Type:        "string",
				Required:    true,
				MaxLength:   100,
				Description: "银行名称",
			},
			"accountName": {
				Type:        "string",
				Required:    true,
				MaxLength:   50,
				Description: "账户名称",
			},
			"operatorId": {
				Type:        "integer",
				Required:    false,
				Min:         floatPtr(1),
				Description: "操作员ID",
			},
		},
		Required: []string{"orderNo", "bankCardNo", "bankName", "accountName"},
	}
}

// GetOrderTransactionListSchema 订单交易流水列表查询参数验证规则
func GetOrderTransactionListSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单交易流水列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderNo": {
				Type:        "string",
				Required:    true,
				Description: "订单编号",
			},
			"type": {
				Type:        "string",
				Required:    false,
				Enum:        []string{"DISBURSEMENT", "WITHHOLD", "REPAYMENT", "REFUND", "PARTIAL_OFFLINE_REPAYMENT"},
				Description: "交易类型",
			},
			"status": {
				Type:        "string",
				Required:    false,
				Enum:        []string{"0", "1", "2"},
				Description: "交易状态：0-处理中，1-成功，2-失败",
			},
			"startDate": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "开始日期（格式：YYYY-MM-DD）",
			},
			"endDate": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "结束日期（格式：YYYY-MM-DD）",
			},
			"page": {
				Type:        "string",
				Required:    false,
				Pattern:     "^[1-9]\\d*$",
				Description: "页码",
				Default:     "1",
			},
			"pageSize": {
				Type:        "string",
				Required:    false,
				Pattern:     "^[1-9]\\d*$",
				Description: "每页数量",
				Default:     "20",
			},
		},
		Required: []string{"orderNo"},
	}
}

// GetCloseOrderSchema 关闭订单参数验证规则
func GetCloseOrderSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "关闭订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderNo": {
				Type:        "string",
				Required:    true,
				Description: "订单编号",
			},
			"reason_for_closure": {
				Type:        "integer",
				Required:    true,
				Min:         floatPtr(0),
				Max:         floatPtr(7),
				Enum:        []string{"0", "1", "2", "3", "4", "5", "6", "7"},
				Description: "关单原因: 0-终审拒绝, 1-法院涉案, 2-纯白户, 3-客户失联, 4-不提供资料, 5-多余订单, 6-重新下单, 7-客户不同意方案",
			},
			"closure_remarks": {
				Type:        "string",
				Required:    false,
				MaxLength:   1000,
				Description: "订单关闭备注",
			},
		},
		Required: []string{"orderNo", "reason_for_closure"},
	}
}

// GetOrderListSchema 订单列表查询参数验证规则
func GetOrderListSchema() jsonschema.Schema {
	minVal := 0.01
	maxVal := *********.99

	return jsonschema.Schema{
		Title: "订单列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "number",
				Required:    false,
				Description: "订单ID",
			},
			"order_no": {
				Type:        "string",
				Required:    false,
				MaxLength:   32,
				Description: "订单编号",
			},
			"user_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "用户ID",
			},
			"product_rule_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "产品规则ID",
			},
			"loan_amount_min": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "申请贷款金额最小值",
			},
			"loan_amount_max": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "申请贷款金额最大值",
			},
			"channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "渠道ID",
			},
			"customer_origin": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "客户来源",
			},
			"initial_order_channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "初始下单渠道ID",
			},
			"payment_channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "支付渠道ID",
			},
			"status": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{8}[0],
				Description: "订单状态: 0-待审核, 1-审核拒绝, 2-待放款, 3-放款中, 4-还款中, 5-已结清, 6-已关闭, 7-放款失败, 8-状态更新异常",
			},
			"is_freeze": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "是否冻结: 0-否; 1-是",
			},
			"is_refund_needed": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "是否需要退款: 0-否; 1-是",
			},
			"complaint_status": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "投诉状态: 0-否; 1-是",
			},
			"user_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "用户姓名",
			},
			"user_id_card": {
				Type:        "string",
				Required:    false,
				MaxLength:   18,
				Description: "用户身份证号",
			},
			"user_mobile": {
				Type:        "string",
				Required:    false,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "用户手机号",
			},
			"audit_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "审核人姓名",
			},
			"reason_for_closure": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{7}[0],
				Description: "关单原因: 0-终审拒绝, 1-法院涉案, 2-纯白户, 3-客户失联, 4-不提供资料, 5-多余订单, 6-重新下单, 7-客户不同意方案",
			},
			"review_status": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{2}[0],
				Description: "复审状态: 0-未复审, 1-复审通过, 2-复审拒绝",
			},
			"sales_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "跟进的业务员ID",
			},
			"is_sales_assigned": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "是否已分配业务员: 0-未分配, 1-已分配",
			},
			"sales_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "业务员姓名",
			},
			"collection_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "当前催收员ID",
			},
			"submitted_at_start": {
				Type:        "string",
				Required:    false,
				Description: "下单时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"submitted_at_end": {
				Type:        "string",
				Required:    false,
				Description: "下单时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"disbursed_at_start": {
				Type:        "string",
				Required:    false,
				Description: "放款时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"disbursed_at_end": {
				Type:        "string",
				Required:    false,
				Description: "放款时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"completed_at_start": {
				Type:        "string",
				Required:    false,
				Description: "结清/关闭时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"completed_at_end": {
				Type:        "string",
				Required:    false,
				Description: "结清/关闭时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)",
			},
			"bill_due_date_start": {
				Type:        "string",
				Required:    false,
				Description: "账单到期时间开始范围 (格式: YYYY-MM-DD)",
			},
			"bill_due_date_end": {
				Type:        "string",
				Required:    false,
				Description: "账单到期时间结束范围 (格式: YYYY-MM-DD)",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Max:         &[]float64{100}[0],
				Description: "每页数量",
				Default:     20,
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

// GetDueBillListSchema 到期账单列表的参数验证规则
func GetDueBillListSchema() jsonschema.Schema {
	//a. 审核姓名。审核人员姓名；b. 催收姓名。催收人员姓名。c. 客户姓名。d. 客户手机号。c。 渠道来源。e. 订单状态。待支付、已支付、逾期已支付、逾期待支付。f. 是否复购。g. 到期时间
	return jsonschema.Schema{
		Title: "到期账单列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{

			"order_no": {
				Type:        "string",
				Required:    false,
				MaxLength:   32,
				Description: "订单编号",
			},
			"channel_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   100,
				Description: "渠道名称",
			},
			"channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "渠道ID",
			},

			"status": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{9}[0],
				Description: "账单状态: 0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清；9-逾期部分支付",
			},

			"user_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "用户姓名",
			},

			"user_mobile": {
				Type:        "string",
				Required:    false,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "用户手机号",
			},
			"sales_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "业务员ID",
			},
			"sales_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "业务员姓名",
			},
			"collection_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "当前催收员ID",
			},
			"collection_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "当前催收员姓名",
			},

			"due_date_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "到期时间开始范围 (格式: YYYY-MM-DD)",
			},
			"due_date_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "到期时间结束范围 (格式: YYYY-MM-DD)",
			},

			"is_repeat_buy": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "是否复购：0-否；1-是",
			},
			"repeat_buy_num": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "复购次数",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Max:         &[]float64{100}[0],
				Description: "每页数量",
				Default:     20,
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

func GetOverdueBillListSchema() jsonschema.Schema {
	//筛选。客户姓名、客户手机、订单编号、渠道来源、是否分配催收、催收姓名、逾期时间、最近催收时间
	return jsonschema.Schema{
		Title: "逾期账单列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    false,
				MaxLength:   32,
				Description: "订单编号",
			},
			"user_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "用户姓名",
			},
			"channel_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   100,
				Description: "渠道名称",
			},
			"channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "渠道ID",
			},
			"user_mobile": {
				Type:        "string",
				Required:    false,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "用户手机号",
			},
			"sales_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "业务员ID",
			},
			"sales_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "业务员姓名",
			},
			"collection_assignee_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "当前催收员ID",
			},
			"collection_assignee_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "当前催收员姓名",
			},
			"overdue_date_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "逾期时间开始范围 (格式: YYYY-MM-DD)",
			},
			"overdue_date_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "逾期时间结束范围 (格式: YYYY-MM-DD)",
			},
			"is_distribute_collection": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{1}[0],
				Description: "是否分配催收：0-否；1-是",
			},
			"collection_time_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "最近催收时间开始范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"collection_time_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "最近催收时间结束范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

func GetListCollectionSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "催收详情列表参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "账单ID",
			},
		},
		Required: []string{"bill_id"},
	}
}

func GetListCollectionOfOrderSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单催收详情列表参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
		},
		Required: []string{"order_id"},
	}
}

func GetDistributeCollectionSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "分配催收参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_ids": {
				Type:        "array",
				Required:    true,
				Description: "账单ID列表",
			},
			"admin_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "管理员ID",
			},
		},
		Required: []string{"bill_ids", "admin_id"},
	}
}

func GetRecordCollectionSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "记录催收参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "账单ID",
			},
			"result": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   50,
				Description: "结果",
			},
			"note": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   512,
				Description: "小记",
			},
		},
		Required: []string{"bill_id", "result", "note"},
	}
}

// GetDisbursementProcessSchema 处理放款的参数验证规则
func GetDisbursementProcessSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "处理放款参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderNo": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   50,
				Description: "订单编号",
			},
			"operatorId": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "操作员ID",
				Default:     0,
			},
		},
		Required: []string{"orderNo"},
	}
}

func GetRefreshDisbursementStatusSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "刷新放款状态参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    true,
				Description: "订单编号",
			},
		},
		Required: []string{"order_no"},
	}
}

// GetOrderAssignSchema 分配订单的参数验证规则（支持批量分配）
func GetOrderAssignSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "分配订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_ids": {
				Type:        "array",
				Required:    true,
				Description: "订单ID数组，最多支持100个订单",
			},
			"sales_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "业务员ID",
			},
			"operator_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "操作员ID",
				Default:     0,
			},
		},
		Required: []string{"order_ids", "sales_id"},
	}
}

// GetOrderClaimSchema 认领订单的参数验证规则
func GetOrderClaimSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "认领订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderId": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
			"salesId": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "业务员ID",
			},
		},
		Required: []string{"orderId", "salesId"},
	}
}

// GetOrderCancelClaimSchema 取消认领订单的参数验证规则
func GetOrderCancelClaimSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "取消认领订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
		},
		Required: []string{"order_id"},
	}
}

// GetManualReviewSchema 人工审核的参数验证规则
func GetManualReviewSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "人工审核参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderId": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
			"salesId": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "业务员ID",
			},
			"approved": {
				Type:        "boolean",
				Required:    true,
				Description: "审核结果：true-通过，false-拒绝",
			},
			"remarks": {
				Type:        "string",
				Required:    false,
				MaxLength:   500,
				Description: "审核备注",
				Default:     "",
			},
		},
		Required: []string{"orderId", "salesId", "approved"},
	}
}

// GetPendingOrdersListSchema 待分配订单列表的参数验证规则
func GetPendingOrdersListSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "待分配订单列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"page": {
				Type:        "string",
				Required:    false,
				Pattern:     "^[1-9]\\d*$",
				Default:     "1",
				Description: "页码（从1开始）",
			},
			"pageSize": {
				Type:        "string",
				Required:    false,
				Pattern:     "^[1-9]\\d*$",
				Default:     "20",
				Description: "每页数量",
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

// GetOrderChannelUpdateSchema 修改订单渠道的参数验证规则
func GetOrderChannelUpdateSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "修改订单渠道参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
			"channel_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "新的渠道ID",
			},
		},
		Required: []string{"order_id", "channel_id"},
	}
}

// GetOrderCustomerInfoSchema 获取下单人信息参数验证规则
func GetOrderCustomerInfoSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "获取下单人信息参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[1-9]\\d*$",
				Description: "订单ID",
			},
		},
		Required: []string{"id"},
	}
}

// GetOrderRemarkCreateSchema 新增订单备注参数验证规则
func GetOrderRemarkCreateSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "新增订单备注参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "订单ID",
			},
			"content": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   1000,
				Description: "备注内容，最多1000字符",
			},
		},
		Required: []string{"order_id", "content"},
	}
}

// GetOrderEarlySettlementSchema 提前结清订单的参数验证规则
func GetOrderEarlySettlementSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "提前结清订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"orderNo": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   50,
				Pattern:     "^[A-Za-z0-9]+$",
				Description: "订单编号",
			},
		},
		Required: []string{"orderNo"},
	}
}

// GetOrderBillInfoSchema 订单账单信息查询参数验证规则
func GetOrderBillInfoSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单账单信息查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   32,
				Description: "订单编号",
			},
		},
		Required: []string{"order_no"},
	}
}

// GetOrderPaymentRecordsSchema 订单支付记录查询参数验证规则
func GetOrderPaymentRecordsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单支付记录查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   32,
				Description: "订单编号",
			},
			"page": {
				Type:        "integer",
				Required:    false,
				Min:         floatPtr(1),
				Description: "页码",
				Default:     1,
			},
			"pageSize": {
				Type:        "integer",
				Required:    false,
				Min:         floatPtr(1),
				Max:         floatPtr(100),
				Description: "每页数量",
				Default:     20,
			},
		},
		Required: []string{"order_no"},
	}
}

// GetUpdateBillDueDateSchema 修改账单时间参数验证规则
func GetUpdateBillDueDateSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "修改账单时间参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "integer",
				Required:    true,
				Min:         floatPtr(1),
				Description: "账单ID（必填，正整数）",
			},
			"due_date": {
				Type:        "string",
				Required:    true,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "新的到期时间（必填，格式：YYYY-MM-DD）",
			},
		},
		Required: []string{"bill_id", "due_date"},
	}
}
