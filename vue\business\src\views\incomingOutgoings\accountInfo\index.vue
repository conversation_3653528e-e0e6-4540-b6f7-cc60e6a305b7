<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import {
  IconRefresh,
  IconSearch,
} from '@arco-design/web-vue/es/icon';
import { getMerchantsBalance } from '@/api/incomingOutgoings';
const queryFormRef = ref();
const queryForm = reactive({});
const accountInfo = reactive({
  total_balance: 0,
  pending_conversion_amount: 0,
  withdrawable_amount: 0,
  overdue_amount: 0,
  future_receivable_amount: 0,
});
const dataSource = ref([{}]);
const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  getMerchantsBalance().then(res => {
    accountInfo.total_balance = res.total_balance;
    accountInfo.pending_conversion_amount = res.pending_conversion_amount;
    accountInfo.withdrawable_amount = res.withdrawable_amount;
    accountInfo.overdue_amount = res.overdue_amount;
    accountInfo.future_receivable_amount = res.future_receivable_amount;
    dataSource.value = res.merchants_balance;
  }).finally(() => {
    loading.value = false;
  })
};
onMounted(() => {
  fetchData();
});
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    slotName: 'no'
  },
  {
    title: '商户名称',
    dataIndex: 'merchant_name',
    width: 260,
  },
  {
    title: '商户类型',
    dataIndex: 'merchant_type',
    width: 120,
  },
  {
    title: '商户号',
    dataIndex: 'mer_no',
    width: 150,
  },
  {
    title: '平台名称',
    dataIndex: 'platform_name',
    width: 100,
  },
  {
    title: '账户总额度',
    dataIndex: 'total_balance',
    width: 120,
  },
  {
    title: '待转化额度',
    dataIndex: 'pending_conversion_amount',
    width: 110,
  },
  {
    title: '可提现额度',
    dataIndex: 'withdrawable_amount',
    width: 150,
  },
  {
    title: '累计逾期金额',
    dataIndex: 'overdue_amount',
    width: 150,
  },
  {
    title: '未来应收金额',
    dataIndex: 'future_receivable_amount',
    width: 150,
  }
];
const handleSearch = () => {
  fetchData();
};
</script>

<template>
  <div class="container">
    <!-- 账户信息 -->
    <a-card title="账户信息" :bordered="false">
      <div class="stat-grid">
        <a-row :gutter="[24, 12]">
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">账户总额度</div>
              <div class="stat-number">{{ accountInfo.total_balance }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">待转化额度</div>
              <div class="stat-number">{{ accountInfo.pending_conversion_amount }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">可提现额度</div>
              <div class="stat-number">{{ accountInfo.withdrawable_amount }}</div>
            </div>
          </a-col>
          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">逾期累计额度</div>
              <div class="stat-number">{{ accountInfo.overdue_amount }}</div>
            </div>
          </a-col>

          <a-col :md="4" :sm="12">
            <div class="stat-item">
              <div class="stat-title">未来应收额度</div>
              <div class="stat-number">{{ accountInfo.future_receivable_amount }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 账户信息表单 -->
<!--      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>-->
    </a-card>
    <!-- 账户信息列表 -->
    <a-card class="table-card" title="账户信息列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: '100%' }"
        :pagination="false"
        :loading="loading"
      >
        <template #no="{ record, rowIndex }">
          {{ rowIndex + 1 }}
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>