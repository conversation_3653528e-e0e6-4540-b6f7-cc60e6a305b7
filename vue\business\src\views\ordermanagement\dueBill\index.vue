<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { IconRefresh, IconSearch, IconUserAdd } from '@arco-design/web-vue/es/icon';
import AllocationCollection from '@/views/ordermanagement/components/allocationCollection/index.vue'
const allocationCollectionRef = ref<any>()
import Withhold from '@/views/ordermanagement/components/withhold/index.vue'
const withholdRef = ref<any>()
import recordCollectionForm from '@/views/ordermanagement/components/recordCollection/form.vue'
const recordCollectionFormRef = ref<any>()
import recordCollectionList from '@/views/ordermanagement/components/recordCollection/list.vue'
import { listDueBills } from '@/api/ordermanagement';
const recordCollectionListRef = ref<any>()
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getChannelList } from '@/api/channel';
const $router = useRouter()
const queryFormRef = ref();
const queryForm = reactive({
  channel_id: '', // 渠道id
  channel_name: '', // 渠道名称
  collection_assignee_id: '', // 催收人id
  collection_assignee_name: '', // 催收人姓名
  due_date_end: dayjs().format('YYYY-MM-DD'), // 到期日期结束
  due_date_start: dayjs().format('YYYY-MM-DD'), // 到期时间开始
  is_repeat_buy: '', // 是否复购
  order_no: '', // 订单编号
  repeat_buy_num: '', // 复购轮次
  sales_assignee_id: '', // 审核人id
  sales_assignee_name: '', // 审核人姓名
  status: '', // 状态，账单状态: 0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清；9-逾期部分支付
  user_mobile: '', // 手机号
  user_name: '', // 用户名
})
// 到期时间
const due_date = ref([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
const handleDueDateChange = (val: any) => {
  queryForm.due_date_start = val[0]
  queryForm.due_date_end = val[1]
}
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
  },
  {
    title: '订单编号',
    dataIndex: 'order_no',
    width: 150,
  },
  {
    title: '渠道来源',
    dataIndex: 'channel_name',
    width: 100,
  },
  {
    title: '审核人员',
    dataIndex: 'sales_assignee_name',
    width: 100,
  },
  {
    title: '催收人员',
    dataIndex: 'collection_assignee_name',
    width: 100,
  },
  {
    title: '是否复购',
    dataIndex: 'is_repeat_buy',
    width: 100,
    render: ({ record }: any) => {
      return record.is_repeat_buy === 1 ? '是' : '否'
    }
  },
  {
    title: '复购次数',
    dataIndex: 'repeat_buy_num',
    width: 100,
  },
  {
    title: '姓名',
    dataIndex: 'user_name',
    width: 130,
  },
  {
    title: '手机号',
    dataIndex: 'user_mobile',
    width: 130,
  },
  {
    title: '期数',
    dataIndex: 'periods',
    width: 100,
  },
  {
    title: '借款金额',
    dataIndex: 'total_due_amount',
    width: 100,
  },
  {
    title: '已付金额',
    dataIndex: 'paid_amount',
    width: 100,
  },
  {
    title: '账单状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '支付时间',
    dataIndex: 'paid_at',
    width: 100,
  },
  {
    title: '使用到期时间',
    dataIndex: 'due_date',
    width: 140,
  },
  {
    title: '操作',
    slotName: 'action',
    width: 300,
    fixed: 'right',
  }
];
const dataSource = ref([{}])
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = () => {
  pagination.current = 1;
  fetchData();
}
const handleReset = async () => {
  queryFormRef.value.resetFields();
  queryForm.due_date_start = ''
  queryForm.due_date_end = ''
  pagination.current = 1;
  fetchData();
}
const loading = ref(false);
const dataTotal = ref({})
const fetchData = async () => {
  loading.value = true;
  listDueBills({
    page: pagination.current,
    page_size: pagination.pageSize,
    ...queryForm
  }).then(res => {
    if(res.code == 0) {
      dataSource.value = res.data.data;
      pagination.total = res.data.total;
      if (res.exdata) {
        dataTotal.value = res.exdata;
      }else{
        dataTotal.value = {}
      }
    }
  }).finally(() => {
    loading.value = false;
  })
}

const dictChannels = ref<any>([]);
onMounted(() => {
  fetchData()
  getChannelList({ page: 1, pageSize: 1000 }).then(res => {
    dictChannels.value = res.data;
  })
})
// 分配催收
const handleAllocationCollection = () => {
  if (selectedKeys.value.length == 0) {
    ElMessage.warning('请选择账单');
    return;
  }
  allocationCollectionRef.value.handleShow(selectedKeys.value)
}
// 批量代扣
const handleWithhold = () => {
  withholdRef.value.handleShow()
}
// 记录催收
const handleRecordCollection = (record: any) => {
  recordCollectionFormRef.value.handleShow(record.id)
}
// 处理跳转详情
const handleToDetail =  (record: any) => {
  $router.push({
    path: `/ordermanagement/Detail/${record.order_id}`,
    query: {
      orderNo: record.order_no,
      uid:  record.user_id,
    }
  })
}
// 查看催收记录
const handleRecordCollectionList = (record: any) => {
  recordCollectionListRef.value.handleShow(record.id)
}
</script>

<template>
<div class="container">
  <!-- 到期账单统计 -->
  <a-card title="到期账单" :bordered="false">
    <div class="stat-grid">
      <a-row :gutter="[24, 12]">
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">应收人数</div>
            <div class="stat-number">{{ dataTotal.receivable_people || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">应收金额</div>
            <div class="stat-number">￥{{ dataTotal.receivable_amount || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">实收人数</div>
            <div class="stat-number">{{ dataTotal.paid_people || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">实收金额</div>
            <div class="stat-number">￥{{ dataTotal.paid_amount || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">当日实收金额</div>
            <div class="stat-number">￥{{ dataTotal.today_paid_amount || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">当日逾期金额</div>
            <div class="stat-number">￥{{ dataTotal.today_overdue_amount || 0 }}</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">回款率</div>
            <div class="stat-number">{{ dataTotal.payment_rate || 0 }}%</div>
          </div>
        </a-col>
        <a-col :md="4" :sm="12">
          <div class="stat-item">
            <div class="stat-title">逾期率</div>
            <div class="stat-number">{{ dataTotal.overdue_rate || 0 }}%</div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 到期账单表单 -->
    <div class="due-form">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        @submit="handleSearch">
        <a-row :gutter="[24, 0]">
          <a-col :md="6" :sm="12">
            <a-form-item field="sales_assignee_name" label="审核人姓名">
              <a-input allow-clear v-model="queryForm.sales_assignee_name" placeholder="审核人姓名"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="collection_assignee_name" label="催收人姓名">
              <a-input allow-clear v-model="queryForm.collection_assignee_name" placeholder="催收人姓名"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="user_name" label="客户姓名">
              <a-input allow-clear v-model="queryForm.user_name" placeholder="下单人姓名"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="user_mobile" label="客户手机号">
              <a-input allow-clear v-model="queryForm.user_mobile"  placeholder="下单人手机号"/>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="channel_id" label="渠道来源">
              <a-select allow-clear v-model="queryForm.channel_id" placeholder="渠道来源">
                <a-option v-for="item in dictChannels" :key="item.id" :value="item.id">{{ item.channel_name }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="status" label="账单状态">
              <a-select allow-clear v-model="queryForm.status" placeholder="订单状态">
                <a-option value="0">待支付</a-option>
                <a-option value="1">已支付</a-option>
                <a-option value="2">逾期已支付</a-option>
                <a-option value="3">逾期待支付</a-option>
                <a-option value="4">已取消</a-option>
                <a-option value="5">已结算</a-option>
                <a-option value="6">已退款</a-option>
                <a-option value="7">部分还款</a-option>
                <a-option value="8">提前结清</a-option>
                <a-option value="9">逾期部分支付</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item field="is_repeat_buy" label="是否复购">
              <a-select allow-clear v-model="queryForm.is_repeat_buy" placeholder="是否复购">
                <a-option :value="1">是</a-option>
                <a-option :value="0">否</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="12">
            <a-form-item label="到期时间">
              <a-range-picker
                allow-clear
                :shortcuts="[
                    {
                      label: '昨日',
                      value: () => [dayjs().subtract(1, 'day'), dayjs()],
                    },
                    {
                      label: '明日',
                      value: () => [dayjs().add(1, 'day'), dayjs()],
                    }
                  ]"
                v-model="due_date"
                @change="handleDueDateChange"/>
            </a-form-item>
          </a-col>
          <a-col :md="24" :sm="24">
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
              <a-button type="primary" status="success" @click="handleAllocationCollection">
                <template #icon>
                  <icon-user-add />
                </template>
                分配催收
              </a-button>
<!--              <a-button type="primary" status="success">
                <template #icon>
                  <icon-user-add />
                </template>
                二次分配催收
              </a-button>
              <a-button type="primary" status="success" @click="handleWithhold">
                <template #icon>
                  <icon-select-all />
                </template>
                批量代扣
              </a-button>
              <a-button type="primary" status="success">
                <template #icon>
                  <icon-send />
                </template>
                到期批量发送短信
              </a-button>
              <a-button type="primary" status="success">
                <template #icon>
                  <icon-send />
                </template>
                提前批量发送短信
              </a-button>-->
            </a-space>
          </a-col>
        </a-row>
      </a-form>

    </div>

  </a-card>
  <!-- 到期账单列表 -->
  <a-card class="table-card" title="到期账单列表" :bordered="false">
    <template #extra>
      <a-space>
        <a-button type="primary" @click="fetchData">
          <template #icon>
            <icon-refresh />
          </template>
          刷新
        </a-button>
      </a-space>
    </template>
    <a-table
      :columns="columns"
      :data="dataSource"
      :pagination="pagination"
      :bordered="false"
      size="medium"
      :scroll="{ y: '500px' }"
      row-key="id"
      :row-selection="rowSelection"
      v-model:selectedKeys="selectedKeys"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      :loading="loading"
    >
      <template #action="{record}">
        <a-button type="text" @click="handleToDetail(record)">处理</a-button>
<!--        <a-button type="text">复购备注</a-button>-->
        <a-button type="text" @click="handleRecordCollection(record)">记录催收</a-button>
        <a-button type="text" @click="handleRecordCollectionList(record)">查看催收</a-button>
<!--        <a-button type="text">记录唤醒</a-button>
        <a-button type="text">查看唤醒</a-button>-->
      </template>
    </a-table>
  </a-card>
  <!-- 分配催收 弹窗 -->
  <allocation-collection ref="allocationCollectionRef" @upload="fetchData"/>
  <!-- 批量代扣 弹窗 -->
  <withhold ref="withholdRef" @upload="fetchData"/>
  <!-- 记录催收 弹窗 -->
  <record-collection-form ref="recordCollectionFormRef" @upload="fetchData"/>
  <!-- 查看催收记录 弹窗 -->
  <record-collection-list ref="recordCollectionListRef" @upload="fetchData"/>
</div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}
.stat-grid{
  .arco-col{
    height: 80px;
  }
  .stat-item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    .stat-title{
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }
    .stat-number{
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top:  10px;
      font-weight: bold;
    }
  }
}
.due-form{
  margin-top: 20px;
}
.table-card{
  margin-top: 10px;
}
</style>