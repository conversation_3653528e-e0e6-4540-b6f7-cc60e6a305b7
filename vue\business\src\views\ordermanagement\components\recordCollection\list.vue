<!-- 查看催收记录 列表 -->
<script setup lang="ts">
import { reactive, ref } from 'vue';
import { listCollection } from '@/api/ordermanagement';

const visible = ref(false)
const recordBillId = ref(0)
const handleShow = (id: number) => {
  recordBillId.value = id
  visible.value = true
  fetchData()
}
const handleOk = () => {
  visible.value = false;
}
const handleCancel = () => {
  visible.value = false;
}
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    render: ({ rowIndex }) => {
      return rowIndex + 1
    }
  },
  {
    title: '记录人',
    dataIndex: 'recorder',
    width: 100,
  },
  {
    title: '记录时间',
    dataIndex: 'record_time',
    width: 180,
  },
  {
    title: '结果',
    dataIndex: 'result',
    width: 100,
  },
  {
    title: '小记',
    dataIndex: 'note',
    width: 100,
  },
]
const dataSource = ref<any>([])
function fetchData() {
  listCollection({
    bill_id: recordBillId.value,
  }).then((res) => {
    console.log(res)
    dataSource.value = res
  })
}
defineExpose({
  handleShow
})
</script>

<template>
  <a-drawer
    title="查看催收记录"
    :visible="visible"
    :width="400"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-table
      :columns="columns"
      :data="dataSource"
      :pagination="false"
    />
  </a-drawer>
</template>

<style scoped lang="less">

</style>