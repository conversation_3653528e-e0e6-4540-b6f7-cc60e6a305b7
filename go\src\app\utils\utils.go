package utils

import (
	"fincore/utils/convert"
	"fincore/utils/jsonschema"
	"fincore/utils/pagination"

	"github.com/gin-gonic/gin"
)

// ParamsValidate 参数校验并获取
func ParamsValidateAndGet(ctx *gin.Context, schemaGeter func() jsonschema.Schema) jsonschema.ValidationResult {
	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		return jsonschema.ValidationResult{
			Valid: false,
			Errors: []jsonschema.ValidationError{
				{
					Message: "请求参数解析失败:" + err.Error(),
				},
			},
		}
	}
	schema := schemaGeter()
	validator := jsonschema.NewValidator(schema)

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}

	}
	return validator.Validate(cleanData)
}

func GetPageReq(params map[string]interface{}) pagination.PaginationRequest {
	// 使用convert包工具函数获取分页参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 20)

	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()
	return paginationReq
}
