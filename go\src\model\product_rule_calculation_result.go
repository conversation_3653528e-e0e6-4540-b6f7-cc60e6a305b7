package model

import "context"

type ProductRuleCalculationResult struct {
	ID                      int     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	ProductRuleID           int     `json:"product_rule_id" db:"product_rule_id"`                       // 产品规则ID
	TotalPeriods            int     `json:"total_periods" db:"total_periods"`                           // 总期数
	TotalPrincipal          float64 `json:"total_principal" db:"total_principal"`                       // 待还本金总额
	TotalInterest           float64 `json:"total_interest" db:"total_interest"`                         // 待还利息总额
	TotalGuaranteeFee       float64 `json:"total_guarantee_fee" db:"total_guarantee_fee"`               // 待还资管费用总额
	TotalOtherFees          float64 `json:"total_other_fees" db:"total_other_fees"`                     // 待还其他费用总额
	TotalAssetManagementFee float64 `json:"total_asset_management_fee" db:"total_asset_management_fee"` // 待还担保费用总额
	TotalRepayableAmount    float64 `json:"total_repayable_amount" db:"total_repayable_amount"`         // 待还总金额
	DisbursementAmount      float64 `json:"disbursement_amount" db:"disbursement_amount"`               // 实际放款金额
	Periods                 string  `json:"periods" db:"periods"`                                       // 计算账单结果
	CreatedAt               int64   `json:"created_at" db:"created_at"`                                 // 创建时间
	UpdatedAt               int64   `json:"updated_at" db:"updated_at"`                                 // 更新时间
	DelatedAt               int64   `json:"delated_at" db:"delated_at"`                                 // 删除时间
}

func (ProductRuleCalculationResult) TableName() string {
	return "product_rule_calculation_result"
}

type ProductRuleCalculationResultService struct {
	ctx context.Context
}

func NewProductRuleCalculationResultService(ctx context.Context) *ProductRuleCalculationResultService {
	return &ProductRuleCalculationResultService{
		ctx: ctx,
	}
}
