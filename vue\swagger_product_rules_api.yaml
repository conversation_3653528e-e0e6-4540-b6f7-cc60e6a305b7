openapi: 3.0.0
info:
  title: FinCore 产品规则管理 API
  description: 产品规则的创建、编辑、查询、删除等功能的完整API接口文档
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 产品规则API服务器（开发环境）
  - url: https://api.fincore.com
    description: 产品规则API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    ProductRuleRequest:
      type: object
      description: 创建产品规则请求体
      properties:
        rule_name:
          type: string
          maxLength: 100
          description: 规则名称
          example: "丰兴61万/2期15点"
        loan_amount:
          type: number
          minimum: 0
          description: 贷款金额（元）
          example: 610000.00
        loan_period:
          type: integer
          minimum: 1
          description: 贷款期限（天）
          example: 60
        total_periods:
          type: integer
          minimum: 1
          description: 总还款期数
          example: 2
        guarantee_fee:
          type: number
          minimum: 0
          description: 担保费（元）
          example: 9150.00
        annual_interest_rate:
          type: number
          minimum: 0
          description: 年利率（%）
          example: 15.00
        other_fees:
          type: number
          minimum: 0
          description: 其他费用（元）
          example: 0.00
        rule_category:
          type: string
          enum: ["新用户", "复购用户"]
          description: 规则类别
          example: "新用户"
        repayment_method:
          type: string
          enum: ["后置付款", "前置付款"]
          description: 还款方式
          example: "后置付款"
        min_risk_score:
          type: number
          minimum: 0
          description: 风控评分下限
          example: 60
        max_risk_score:
          type: number
          minimum: 0
          description: 风控评分上限
          example: 100
        pre_principal:
          type: number
          minimum: 0
          description: 前置本金（元）
          example: 0.00
        pre_interest:
          type: number
          minimum: 0
          description: 前置利息（元）
          example: 0.00
        pre_guarantee_fee:
          type: number
          minimum: 0
          description: 前置担保费（元）
          example: 0.00
      required:
        - rule_name
        - loan_amount
        - loan_period
        - total_periods
        - annual_interest_rate
        - rule_category

    ProductRuleUpdateRequest:
      allOf:
        - $ref: '#/components/schemas/ProductRuleRequest'
        - type: object
          properties:
            id:
              type: integer
              description: 产品规则ID
              example: 1
          required:
            - id

    RepaymentPeriod:
      type: object
      description: 单期还款计划
      properties:
        period_number:
          type: integer
          description: 期数
          example: 1
        due_principal:
          type: number
          format: float
          description: 应还本金
          example: 3333.34
        due_interest:
          type: number
          format: float
          description: 应还利息
          example: 156.62
        due_guarantee_fee:
          type: number
          format: float
          description: 应还担保费
          example: 166.67
        due_other_fees:
          type: number
          format: float
          description: 应还其他费用
          example: 33.34
        asset_management_fee:
          type: number
          format: float
          description: 资管费
          example: 3489.96
        late_fee:
          type: number
          format: float
          description: 逾期费
          example: 0
        total_due_amount:
          type: number
          format: float
          description: 当期应还总额
          example: 3656.63
        due_date:
          type: string
          format: date
          description: 到期日期
          example: "2025-09-19"

    RepaymentSchedule:
      type: object
      description: 完整还款计划
      properties:
        total_periods:
          type: integer
          description: 总期数
          example: 3
        periods:
          type: array
          description: 各期还款计划
          items:
            $ref: '#/components/schemas/RepaymentPeriod'
          example:
            - period_number: 1
              due_principal: 3333.34
              due_interest: 156.62
              due_guarantee_fee: 166.67
              due_other_fees: 33.34
              asset_management_fee: 3489.96
              late_fee: 0
              total_due_amount: 3656.63
              due_date: "2025-09-19"
            - period_number: 2
              due_principal: 3333.34
              due_interest: 156.62
              due_guarantee_fee: 166.67
              due_other_fees: 33.34
              asset_management_fee: 3489.96
              late_fee: 0
              total_due_amount: 3656.63
              due_date: "2025-10-19"
            - period_number: 3
              due_principal: 3333.32
              due_interest: 156.63
              due_guarantee_fee: 166.66
              due_other_fees: 33.34
              asset_management_fee: 3489.96
              late_fee: 0
              total_due_amount: 3656.61
              due_date: "2025-11-18"
        total_principal:
          type: number
          format: float
          description: 总本金
          example: 10000
        total_interest:
          type: number
          format: float
          description: 总利息
          example: 369.87
        total_guarantee_fee:
          type: number
          format: float
          description: 总担保费
          example: 500
        total_other_fees:
          type: number
          format: float
          description: 总其他费用
          example: 100
        total_asset_management_fee:
          type: number
          format: float
          description: 总资管费
          example: 10469.87
        total_repayable_amount:
          type: number
          format: float
          description: 总应还金额
          example: 10969.87
        disbursement_amount:
          type: number
          format: float
          description: 实际放款金额
          example: 10000

    ProductRuleResponse:
      type: object
      description: 产品规则响应数据
      properties:
        id:
          type: integer
          description: 产品规则ID
          example: 1
        rule_name:
          type: string
          description: 规则名称
          example: "丰兴61万/2期15点"
        loan_amount:
          type: number
          description: 贷款金额（元）
          example: 610000.00
        loan_period:
          type: integer
          description: 贷款期限（天）
          example: 60
        total_periods:
          type: integer
          description: 总还款期数
          example: 2
        guarantee_fee:
          type: number
          description: 担保费（元）
          example: 9150.00
        annual_interest_rate:
          type: number
          description: 年利率（%）
          example: 15.00
        other_fees:
          type: number
          description: 其他费用（元）
          example: 0.00
        rule_category:
          type: string
          description: 规则类别
          example: "新用户"
        repayment_method:
          type: string
          description: 还款方式
          example: "后置付款"
        created_at:
          type: integer
          description: 创建时间（Unix时间戳）
          example: 1701234567
        updated_at:
          type: integer
          description: 更新时间（Unix时间戳）
          example: 1701234567

    ProductRuleWithCalculation:
      type: object
      description: 包含计算结果的产品规则
      properties:
        product_rule:
          $ref: '#/components/schemas/ProductRuleResponse'
        custom_calculation:
          $ref: '#/components/schemas/RepaymentSchedule'

    ProductRuleListResponse:
      type: object
      description: 产品规则列表响应
      properties:
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 10
        total:
          type: integer
          description: 总记录数
          example: 25
        items:
          type: array
          description: 产品规则列表
          items:
            $ref: '#/components/schemas/ProductRuleResponse'

    DeleteRequest:
      type: object
      description: 删除请求体
      properties:
        ids:
          type: array
          items:
            type: integer
          description: 要删除的产品规则ID数组
          example: [1, 2, 3]
      required:
        - ids

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for user authentication

paths:
  /business/productrules/list/createRule:
    post:
      summary: 创建产品规则
      description: |
        创建新的产品规则，包含贷款金额、期限、利率等信息。

        **功能特点：**
        - 支持自定义规则名称和贷款参数
        - 自动计算还款计划和各期金额
        - 支持前置付款和后置付款两种还款方式
        - 包含完整的参数验证
      tags:
        - 产品规则管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_rule:
                  $ref: '#/components/schemas/ProductRuleRequest'
                custom_calculation:
                  $ref: '#/components/schemas/RepaymentSchedule'
            example:
              product_rule:
                rule_name: "测试产品规则"
                loan_amount: 10000.00
                loan_period: 30
                total_periods: 3
                guarantee_fee: 500.00
                annual_interest_rate: 15.00
                other_fees: 100.00
                rule_category: "新用户"
                repayment_method: "后置付款"
              custom_calculation:
                total_periods: 3
                periods:
                  - period_number: 1
                    due_principal: 3333.34
                    due_interest: 156.62
                    due_guarantee_fee: 166.67
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.63
                    due_date: "2025-09-19"
                  - period_number: 2
                    due_principal: 3333.34
                    due_interest: 156.62
                    due_guarantee_fee: 166.67
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.63
                    due_date: "2025-10-19"
                  - period_number: 3
                    due_principal: 3333.32
                    due_interest: 156.63
                    due_guarantee_fee: 166.66
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.61
                    due_date: "2025-11-18"
                total_principal: 10000
                total_interest: 369.87
                total_guarantee_fee: 500
                total_other_fees: 100
                total_asset_management_fee: 10469.87
                total_repayable_amount: 10969.87
                disbursement_amount: 10000
      responses:
        '200':
          description: 创建产品规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        nullable: true
              example:
                code: 0
                message: "创建产品规则成功"
                data: null
                time: 1701234567
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "创建产品规则失败"
                data: "规则名称不能为空"
                time: 1701234567

  /business/productrules/list/updateRule:
    post:
      summary: 编辑产品规则
      description: |
        编辑现有的产品规则信息。

        **功能特点：**
        - 支持修改所有产品规则参数
        - 重新计算还款计划
        - 包含完整的参数验证
        - 需要提供产品规则ID
      tags:
        - 产品规则管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_rule:
                  $ref: '#/components/schemas/ProductRuleUpdateRequest'
                custom_calculation:
                  $ref: '#/components/schemas/RepaymentSchedule'
            example:
              product_rule:
                id: 1
                rule_name: "测试产品规则（修改版）"
                loan_amount: 10000.00
                loan_period: 30
                total_periods: 3
                guarantee_fee: 500.00
                annual_interest_rate: 15.00
                other_fees: 100.00
                rule_category: "新用户"
                repayment_method: "后置付款"
              custom_calculation:
                total_periods: 3
                periods:
                  - period_number: 1
                    due_principal: 3333.34
                    due_interest: 156.62
                    due_guarantee_fee: 166.67
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.63
                    due_date: "2025-09-19"
                  - period_number: 2
                    due_principal: 3333.34
                    due_interest: 156.62
                    due_guarantee_fee: 166.67
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.63
                    due_date: "2025-10-19"
                  - period_number: 3
                    due_principal: 3333.32
                    due_interest: 156.63
                    due_guarantee_fee: 166.66
                    due_other_fees: 33.34
                    asset_management_fee: 3489.96
                    late_fee: 0
                    total_due_amount: 3656.61
                    due_date: "2025-11-18"
                total_principal: 10000
                total_interest: 369.87
                total_guarantee_fee: 500
                total_other_fees: 100
                total_asset_management_fee: 10469.87
                total_repayable_amount: 10969.87
                disbursement_amount: 10000
      responses:
        '200':
          description: 编辑产品规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 产品规则ID
                            example: 1
                          data:
                            $ref: '#/components/schemas/ProductRuleWithCalculation'
              example:
                code: 0
                message: "产品规则更新成功"
                data:
                  id: 1
                  data:
                    product_rule:
                      id: 1
                      rule_name: "丰兴61万/2期15点（修改版）"
                      loan_amount: 610000.00
                      loan_period: 60
                      total_periods: 2
                      guarantee_fee: 9150.00
                      annual_interest_rate: 15.00
                      other_fees: 0.00
                      rule_category: "新用户"
                      repayment_method: "后置付款"
                      created_at: 1701234567
                      updated_at: 1701234600
                time: 1701234600
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "编辑产品参数校验失败"
                data: "产品规则ID不能为空"
                time: 1701234567
        '404':
          description: 产品规则不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "编辑产品规则失败"
                data: "产品规则不存在"
                time: 1701234567

  /business/productrules/list/getRules:
    get:
      summary: 查询产品规则列表
      description: |
        分页查询产品规则列表，支持多种筛选条件。

        **查询功能：**
        - 支持按规则名称筛选
        - 支持按贷款期限筛选
        - 支持按规则类别筛选
        - 支持按还款方式筛选
        - 支持分页查询
      tags:
        - 产品规则管理
      parameters:
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: limit
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          example: 10
        - name: rule_name
          in: query
          description: 规则名称（精确匹配）
          required: false
          schema:
            type: string
          example: "丰兴61万/2期15点"
        - name: loan_period
          in: query
          description: 贷款期限（天）
          required: false
          schema:
            type: integer
          example: 60
        - name: rule_category
          in: query
          description: 规则类别
          required: false
          schema:
            type: string
            enum: ["新用户", "复购用户"]
          example: "新用户"
        - name: repayment_method
          in: query
          description: 还款方式
          required: false
          schema:
            type: string
            enum: ["后置付款", "前置付款"]
          example: "后置付款"
      responses:
        '200':
          description: 查询产品规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ProductRuleListResponse'
              example:
                code: 0
                message: "查询产品规则成功"
                data:
                  page: 1
                  pageSize: 10
                  total: 3
                  items:
                    - id: 1
                      rule_name: "丰兴61万/2期15点"
                      loan_amount: 610000.00
                      loan_period: 60
                      total_periods: 2
                      guarantee_fee: 9150.00
                      annual_interest_rate: 15.00
                      other_fees: 0.00
                      rule_category: "新用户"
                      repayment_method: "后置付款"
                      created_at: 1701234567
                      updated_at: 1701234567
                    - id: 2
                      rule_name: "复购用户优惠方案"
                      loan_amount: 500000.00
                      loan_period: 30
                      total_periods: 1
                      guarantee_fee: 7500.00
                      annual_interest_rate: 12.00
                      other_fees: 0.00
                      rule_category: "复购用户"
                      repayment_method: "前置付款"
                      created_at: 1701234567
                      updated_at: 1701234567
                time: 1701234567
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "查询产品规则失败"
                data: "页码必须大于0"
                time: 1701234567

  /business/productrules/list/delRule:
    delete:
      summary: 删除产品规则
      description: |
        批量删除产品规则。

        **删除功能：**
        - 支持批量删除多个产品规则
        - 返回实际可删除的ID列表
        - 自动跳过不存在或不可删除的规则
        - 包含完整的错误处理
      tags:
        - 产品规则管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteRequest'
            example:
              ids: [1, 2, 3]
      responses:
        '200':
          description: 删除产品规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: integer
                        description: 实际删除的产品规则ID列表
              example:
                code: 0
                message: "删除成功"
                data: [1, 2]
                time: 1701234567
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "删除产品规则失败"
                data: "删除ID列表不能为空"
                time: 1701234567
        '403':
          description: 删除失败（规则被使用中）
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 1
                message: "删除产品规则失败"
                data: "产品规则正在被使用，无法删除"
                time: 1701234567

security:
  - BearerAuth: []

tags:
  - name: 产品规则管理
    description: 产品规则的创建、编辑、查询、删除等管理功能
