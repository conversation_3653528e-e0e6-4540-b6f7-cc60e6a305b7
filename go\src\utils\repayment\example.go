package repayment

import (
	"encoding/json"
	"fmt"

	"fincore/model"
)

// RepaymentPreviewRequest 还款预览请求结构
type RepaymentPreviewRequest struct {
	LoanAmount    float64 `json:"loan_amount" binding:"required,min=0.01"`  // 申请贷款金额
	ProductRuleID int     `json:"product_rule_id" binding:"required,min=1"` // 产品规则ID
}

// RepaymentPreviewResponse 还款预览响应结构
type RepaymentPreviewResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    *RepaymentSchedule `json:"data,omitempty"`
}

// PreviewRepaymentSchedule 预览还款计划（用于创单前展示）
// 这是一个示例方法，展示如何在其他场景中使用公共的还款计算逻辑
func PreviewRepaymentSchedule(request RepaymentPreviewRequest) RepaymentPreviewResponse {
	// 1. 根据产品规则ID查询产品规则
	productRule, err := getProductRuleByID(request.ProductRuleID)
	if err != nil {
		return RepaymentPreviewResponse{
			Success: false,
			Message: fmt.Sprintf("查询产品规则失败: %v", err),
		}
	}

	// 2. 临时设置产品规则的贷款金额（如果需要使用用户输入的金额）
	if request.LoanAmount != productRule.LoanAmount {
		// 创建一个副本以避免修改原始数据
		tempRule := *productRule
		tempRule.LoanAmount = request.LoanAmount
		productRule = &tempRule
	}

	// 3. 使用公共方法计算还款计划
	schedule, err := CalculateRepaymentSchedule(productRule)
	if err != nil {
		return RepaymentPreviewResponse{
			Success: false,
			Message: fmt.Sprintf("计算还款计划失败: %v", err),
		}
	}

	// 3. 返回计算结果
	return RepaymentPreviewResponse{
		Success: true,
		Message: "计算成功",
		Data:    schedule,
	}
}

// getProductRuleByID 根据ID查询产品规则（示例实现）
func getProductRuleByID(ruleID int) (*model.ProductRules, error) {
	// 这里应该是实际的数据库查询逻辑
	// 为了示例，我们返回一个模拟的产品规则
	if ruleID <= 0 {
		return nil, fmt.Errorf("无效的产品规则ID")
	}

	// 模拟数据库查询
	productRule := &model.ProductRules{
		ID:                 ruleID,
		RuleName:           "示例产品规则",
		LoanAmount:         10000.00,
		LoanPeriod:         30, // 借款周期30天
		TotalPeriods:       3,  // 支持3期还款（总还款天数=30*3=90天）
		GuaranteeFee:       500.00,
		AnnualInterestRate: 15.0,
		OtherFees:          100.00,
		RuleCategory:       "新用户",
		RepaymentMethod:    "后置付款",
	}

	return productRule, nil
}

// ExampleUsage 使用示例
func ExampleUsage() {
	// 示例1：预览还款计划
	request := RepaymentPreviewRequest{
		LoanAmount:    8000.00,
		ProductRuleID: 1,
	}

	response := PreviewRepaymentSchedule(request)
	if response.Success {
		fmt.Println("还款计划预览成功:")

		// 将结果转换为JSON格式输出
		jsonData, _ := json.MarshalIndent(response.Data, "", "  ")
		fmt.Println(string(jsonData))
	} else {
		fmt.Printf("还款计划预览失败: %s\n", response.Message)
	}

	// 示例2：直接计算还款计划
	productRule := &model.ProductRules{
		ID:                 1,
		RuleName:           "测试规则",
		LoanAmount:         5000.00,
		LoanPeriod:         15,
		TotalPeriods:       1,
		GuaranteeFee:       200.00,
		AnnualInterestRate: 12.0,
		OtherFees:          50.00,
		RuleCategory:       "新用户",
		RepaymentMethod:    "前置付款",
	}

	schedule, err := CalculateRepaymentSchedule(productRule)
	if err != nil {
		fmt.Printf("计算失败: %v\n", err)
		return
	}

	fmt.Printf("\n直接计算结果:\n")
	fmt.Printf("总期数: %d\n", schedule.TotalPeriods)
	fmt.Printf("总本金: %.2f\n", schedule.TotalPrincipal)
	fmt.Printf("总利息: %.2f\n", schedule.TotalInterest)
	fmt.Printf("总担保费: %.2f\n", schedule.TotalGuaranteeFee)
	fmt.Printf("总应还金额: %.2f\n", schedule.TotalRepayableAmount)
	fmt.Printf("实际放款金额: %.2f\n", schedule.DisbursementAmount)

	for _, period := range schedule.Periods {
		fmt.Printf("第%d期: 应还%.2f元, 到期日%s\n",
			period.PeriodNumber,
			period.TotalDueAmount,
			period.DueDate)
	}
}

// RepaymentCalculatorService 还款计算服务（可以作为独立的服务使用）
type RepaymentCalculatorService struct{}

// NewRepaymentCalculatorService 创建还款计算服务实例
func NewRepaymentCalculatorService() *RepaymentCalculatorService {
	return &RepaymentCalculatorService{}
}

// Calculate 计算还款计划
func (s *RepaymentCalculatorService) Calculate(productRule *model.ProductRules) (*RepaymentSchedule, error) {
	return CalculateRepaymentSchedule(productRule)
}

// Preview 预览还款计划
func (s *RepaymentCalculatorService) Preview(request RepaymentPreviewRequest) RepaymentPreviewResponse {
	return PreviewRepaymentSchedule(request)
}

// ConvertToBillMaps 转换为账单数据 map
func (s *RepaymentCalculatorService) ConvertToBillMaps(schedule *RepaymentSchedule, orderID, userID int) []map[string]interface{} {
	return ConvertToRepaymentBillMaps(schedule, orderID, userID)
}
