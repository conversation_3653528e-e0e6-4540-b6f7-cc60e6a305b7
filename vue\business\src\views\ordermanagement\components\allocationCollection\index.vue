<!-- 分配催收 弹窗 -->
<script setup lang="ts">
import { reactive, ref } from 'vue';
import { getList } from '@/api/system/account';
import { ElMessage } from 'element-plus';
import { distributeCollection } from '@/api/ordermanagement';

const visible = ref(false)
const setBillIds = ref([])
const handleShow = async (selectedKeys: any) => {
  setBillIds.value = selectedKeys;
  await fetchData();
  visible.value = true;
};

const handleOk = () => {
  if(selectedKeys.value.length == 0){
    ElMessage.error('请选择用户');
    return;
  }
  distributeCollection({
    bill_ids: setBillIds.value,
    admin_id: selectedKeys.value[0]
  }).then(result => {
    ElMessage.success('分配成功');
    visible.value = false;
    setBillIds.value = [];
    selectedKeys.value = [];
    emit('upload')
  })
};
const handleCancel = () => {
  visible.value = false;
}
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    render: ({ rowIndex }: { rowIndex: number }) => {
      return rowIndex+1
    }
  },
  {
    title: '成员账号',
    dataIndex: 'username',
    width: 150,
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '邮箱地址',
    dataIndex: 'email',
  },
  {
    title: '是否启用',
    dataIndex: 'status',
    width: 100,
    render: ({ record }: { record: any }) => {
      return record.status == 0 ? '是' : '否';
    }
  }
];
const dataSource = ref([])
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'radio'
});
async function fetchData() {
  const data= await getList({page:1,pageSize:1000});
  dataSource.value = data.items;
}
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};
const emit = defineEmits(['upload']);
defineExpose({
  handleShow
});
</script>

<template>
  <a-drawer width="50%" :visible="visible" @ok="handleOk" @cancel="handleCancel" unmountOnClose>
    <template #title>
      分配催收-选择用户
    </template>
    <!-- 催收人员列表   -->
    <a-table
      row-key="id"
      :columns="columns"
      :data="dataSource"
      :pagination="pagination"
      :row-selection="rowSelection"
      v-model:selectedKeys="selectedKeys"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />
  </a-drawer>
</template>

<style scoped lang="less">

</style>