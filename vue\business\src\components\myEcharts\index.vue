<!-- ECharts.vue -->
<template>
  <div ref="chartRef" :style="{ width: width, height: height }"></div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';

export default {
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '400px'
    },
    option: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const chartRef = ref(null);
    let chart = null;

    const initChart = () => {
      if (!chartRef.value) return;

      // 初始化图表
      chart = echarts.init(chartRef.value);
      chart.setOption(props.option);

      // 响应式调整
      window.addEventListener('resize', resizeChart);
    };

    const resizeChart = () => {
      if (chart) {
        chart.resize();
      }
    };

    onMounted(() => {
      initChart();
    });

    onBeforeUnmount(() => {
      if (chart) {
        window.removeEventListener('resize', resizeChart);
        chart.dispose();
        chart = null;
      }
    });

    // 监听 option 变化
    watch(() => props.option, (newVal) => {
      if (chart) {
        chart.setOption(newVal);
      }
    }, { deep: true });

    return {
      chartRef
    };
  }
};
</script>