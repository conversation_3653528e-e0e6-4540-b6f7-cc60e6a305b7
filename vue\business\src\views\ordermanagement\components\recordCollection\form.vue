<!-- 记录催收 弹窗 form 表单-结果，备注 --->
<script setup lang="ts">
import { reactive, ref } from 'vue';
  import { recordCollection } from '@/api/ordermanagement';
  import { ElMessage } from 'element-plus';
  const visible = ref(false);
  const recordBillId = ref(0);
  const handleShow = (id: number) => {
    recordBillId.value = id;
    visible.value = true;
  };
  const recordCollectionFormRef = ref();
  const handleOk = async ({values, errors}) => {
    console.log(values);
    console.log(errors);
    if(errors) {
      return false;
    }
    recordCollection({
      bill_id: recordBillId.value,
      result: form.result,
      note: form.note,
    }).then((res) => {
      ElMessage.success('记录催收成功');
      visible.value = false;
      recordCollectionFormRef.value.resetFields();
    });
  };
  const handleCancel = () => {
    visible.value = false;
  };
  const form = reactive({
    result: '',
    note: '',
  });
  defineExpose({
    handleShow,
  });
</script>

<template>
  <a-modal
    title="记录催收"
    v-model:visible="visible"
    :width="400"
    :footer="false"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="recordCollectionFormRef" @submit="handleOk">
      <a-form-item
        field="result"
        label="结果"
        :rules="[{ required: true, message: '请选择结果' }]"
      >
        <a-select v-model="form.result">
          <a-option>承诺还款</a-option>
          <a-option>申请延期还款</a-option>
          <a-option>拒绝还款</a-option>
          <a-option>电话无人接听</a-option>
          <a-option>电话拒接</a-option>
          <a-option>电话关机</a-option>
          <a-option>电话停机</a-option>
          <a-option>客户失联</a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="note"
        label="小记"
        :rules="[{ required: true, message: '请输入小记' }]"
      >
        <a-input v-model="form.note" />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" html-type="submit">提交</a-button>
          <a-button @click="recordCollectionFormRef.resetFields()">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less"></style>