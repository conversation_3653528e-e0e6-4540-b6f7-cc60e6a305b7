package productrules

import (
	"context"
	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/log"

	"github.com/gogf/gf/v2/util/gconv"
)

type ProductRuleRepository struct {
	ctx    context.Context
	logger *log.Logger
}

func NewProductRuleRepository(ctx context.Context) *ProductRuleRepository {
	return &ProductRuleRepository{
		ctx:    ctx,
		logger: log.RegisterModule("productrules", "产品规则模块").WithContext(ctx),
	}
}

// InsertProductRuleAndCalculationResult 插入产品规则和规则账单计算结果
func (r *ProductRuleRepository) InsertProductRuleAndCalculationResult(tx gform.IOrm, rule *ProductRule) (err error) {

	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}

	// 插入产品规则
	_, err = tx.Table("product_rules").Data(gconv.Map(rule.ProductRule)).Insert()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("插入产品规则失败")
		return
	}

	// 插入规则账单计算结果
	_, err = tx.Table("product_rule_calculation_result").Data(gconv.Map(rule.CustomerCalculation)).Insert()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("插入规则账单计算结果失败")
		return
	}
	return
}

// UpdateProductRuleAndCalculationResult 更新产品规则和规则账单计算结果
func (r *ProductRuleRepository) UpdateProductRuleAndCalculationResult(tx gform.IOrm, rule map[string]interface{}, calulation map[string]interface{}) (err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}

	// 更新产品规则
	_, err = tx.Table("product_rules").Data(rule).Where("id", rule["id"]).Update()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("更新产品规则失败")
		return
	}

	// 更新规则账单计算结果
	_, err = tx.Table("product_rule_calculation_result").Data(calulation).Where("product_rule_id", rule["id"]).Update()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("更新规则账单计算结果失败")
		return
	}

	return

}

// GetProductRuleInProgressOrders 查询产品在途订单
type GetProductRuleInProgressOrdersParams struct {
	IDs []uint `json:"ids"`
}

// GetProductRuleInProgressOrders 查询产品在途订单
func (r *ProductRuleRepository) GetProductRuleInProgressOrders(tx gform.IOrm, params GetProductRuleInProgressOrdersParams) (list []gform.Data, err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}
	list, err = tx.Table("business_loan_order").
		WhereIn("product_rule_id", []interface{}{params.IDs}).
		WhereIn("status", []interface{}{model.OrderStatusPendingDisbursement, model.OrderStatusDisbursed}).Get()
	if err != nil {
		r.logger.WithFields(log.String("error", err.Error())).Error("查询产品是否存在在途订单失败")
		return
	}

	return
}

// DelProducts 删除产品
func (r *ProductRuleRepository) DelProducts(tx gform.IOrm, IDs []uint) (err error) {
	if tx == nil {
		tx = model.DB(model.WithContext(r.ctx))
	}

	_, err = tx.Table("product_rules").WhereIn("id", []interface{}{IDs}).Delete()
	return
}
